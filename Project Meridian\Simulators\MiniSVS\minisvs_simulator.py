import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import socket
import threading
import time
import random
import queue

# Constants for pressure conversion (approximate for seawater)
DBAR_PER_METER = 0.980665
DBAR_PER_FOOT = 0.3048 * DBAR_PER_METER

class ValeportMiniSVSSimulator:
    def __init__(self, master):
        self.master = master
        master.title("Valeport miniSVS Simulator (Surface Multibeam)")
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # --- Simulation State ---
        self.simulation_running = False
        self.simulation_thread = None
        self.tcp_server_socket = None
        self.tcp_accept_thread = None
        self.tcp_client_sockets = []
        self.gui_queue = queue.Queue()

        # --- Ramp State Variables ---
        self.ramp_sv_start_time = 0
        self.ramp_sv_initial_value = 0
        self.ramp_p_start_time = 0
        self.ramp_p_initial_value = 0
        self.ramp_t_start_time = 0
        self.ramp_t_initial_value = 0
        
        self.setup_variables()
        self.setup_gui()
        self.check_gui_queue() # Start checking the queue for GUI updates from threads

    def setup_variables(self):
        # --- Sensor Configuration Variables ---
        self.sensor_type_var = tk.StringVar(value="SV only")
        
        # SV
        self.sv_mode_var = tk.StringVar(value="Static")
        self.sv_static_var = tk.StringVar(value="1500.000")
        self.sv_min_var = tk.StringVar(value="1480.000")
        self.sv_max_var = tk.StringVar(value="1550.000")
        self.sv_ramp_start_var = tk.StringVar(value="1490.000")
        self.sv_ramp_end_var = tk.StringVar(value="1510.000")
        self.sv_ramp_duration_var = tk.StringVar(value="60")

        # Pressure
        self.p_mode_var = tk.StringVar(value="Static")
        self.p_static_var = tk.StringVar(value="0.500")
        self.p_unit_var = tk.StringVar(value="dBar")
        self.p_min_var = tk.StringVar(value="0.100")
        self.p_max_var = tk.StringVar(value="5.000")
        self.p_ramp_start_var = tk.StringVar(value="0.200")
        self.p_ramp_end_var = tk.StringVar(value="1.000")
        self.p_ramp_duration_var = tk.StringVar(value="60")
        self.p_fs_var = tk.StringVar(value="100") # dBar
        self.p_format_label_var = tk.StringVar(value="PP.PPP") # Initial based on 100 dBar FS

        # Temperature
        self.t_mode_var = tk.StringVar(value="Static")
        self.t_static_var = tk.StringVar(value="18.000")
        self.t_min_var = tk.StringVar(value="5.000")
        self.t_max_var = tk.StringVar(value="30.000")
        self.t_ramp_start_var = tk.StringVar(value="17.000")
        self.t_ramp_end_var = tk.StringVar(value="19.000")
        self.t_ramp_duration_var = tk.StringVar(value="60")

        # --- Output Configuration Variables ---
        self.data_format_var = tk.StringVar(value="Valeport Standard")
        self.protocol_var = tk.StringVar(value="UDP")
        self.ip_address_var = tk.StringVar(value="127.0.0.1")
        self.udp_broadcast_var = tk.BooleanVar(value=False)
        self.port_var = tk.StringVar(value="12345")
        self.update_rate_var = tk.StringVar(value="1") # Hz

        # --- Status Variables ---
        self.status_var = tk.StringVar(value="Idle")
        self.tcp_clients_var = tk.StringVar(value="Connected Clients (TCP): 0")

    def setup_gui(self):
        # Main frame
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.master.columnconfigure(0, weight=1)
        self.master.rowconfigure(0, weight=1)

        # Sensor Configuration Frame
        self.create_sensor_config_frame(main_frame)

        # Output Configuration Frame
        self.create_output_config_frame(main_frame)

        # Simulation Control & Status Frame
        self.create_control_status_frame(main_frame)

        # Initial state updates
        self.on_sensor_type_change()
        self.on_pressure_fs_change()
        self.on_protocol_change()
        self.on_udp_broadcast_toggle()


    def create_sensor_config_frame(self, parent_frame):
        sensor_frame = ttk.LabelFrame(parent_frame, text="Sensor Configuration", padding="10")
        sensor_frame.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        ttk.Label(sensor_frame, text="Sensor Type:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        sensor_type_combo = ttk.Combobox(sensor_frame, textvariable=self.sensor_type_var, 
                                         values=["SV only", "SV+P", "SV+T"], state="readonly", width=15)
        sensor_type_combo.grid(row=0, column=1, columnspan=2, sticky=tk.W, padx=5, pady=2)
        sensor_type_combo.bind("<<ComboboxSelected>>", self.on_sensor_type_change)

        # --- SV Section ---
        sv_sub_frame = ttk.LabelFrame(sensor_frame, text="Sound Velocity (SV)", padding="5")
        sv_sub_frame.grid(row=1, column=0, columnspan=4, padx=5, pady=5, sticky=(tk.W, tk.E))
        
        ttk.Label(sv_sub_frame, text="Mode:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Combobox(sv_sub_frame, textvariable=self.sv_mode_var, values=["Static", "Ramp", "Random"], state="readonly", width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(sv_sub_frame, text="Static (m/s):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_static_var, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(sv_sub_frame, text="Min (m/s):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_min_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(sv_sub_frame, text="Max (m/s):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_max_var, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(sv_sub_frame, text="Ramp Start (m/s):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_ramp_start_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(sv_sub_frame, text="Ramp End (m/s):").grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_ramp_end_var, width=10).grid(row=2, column=3, sticky=tk.W, padx=5, pady=2)
        ttk.Label(sv_sub_frame, text="Ramp Duration (s):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(sv_sub_frame, textvariable=self.sv_ramp_duration_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        # --- Pressure Section ---
        self.p_sub_frame = ttk.LabelFrame(sensor_frame, text="Pressure (P)", padding="5")
        self.p_sub_frame.grid(row=2, column=0, columnspan=4, padx=5, pady=5, sticky=(tk.W, tk.E))

        ttk.Label(self.p_sub_frame, text="Mode:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Combobox(self.p_sub_frame, textvariable=self.p_mode_var, values=["Static", "Ramp", "Random"], state="readonly", width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.p_sub_frame, text="Static Value:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_static_var, width=7).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        ttk.Combobox(self.p_sub_frame, textvariable=self.p_unit_var, values=["dBar", "m", "ft"], state="readonly", width=5).grid(row=0, column=4, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(self.p_sub_frame, text="Min Value:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_min_var, width=7).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.p_sub_frame, text="Max Value:").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_max_var, width=7).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.p_sub_frame, text="Ramp Start:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_ramp_start_var, width=7).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.p_sub_frame, text="Ramp End:").grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_ramp_end_var, width=7).grid(row=2, column=3, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.p_sub_frame, text="Ramp Duration (s):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.p_sub_frame, textvariable=self.p_ramp_duration_var, width=7).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.p_sub_frame, text="Pressure Range (FS, dBar):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        p_fs_combo = ttk.Combobox(self.p_sub_frame, textvariable=self.p_fs_var, values=["100", "500", "1000", "6000"], state="readonly", width=7)
        p_fs_combo.grid(row=4, column=1, sticky=tk.W, padx=5, pady=2)
        p_fs_combo.bind("<<ComboboxSelected>>", self.on_pressure_fs_change)
        ttk.Label(self.p_sub_frame, text="Format:").grid(row=4, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.p_sub_frame, textvariable=self.p_format_label_var).grid(row=4, column=3, sticky=tk.W, padx=5, pady=2)

        # --- Temperature Section ---
        self.t_sub_frame = ttk.LabelFrame(sensor_frame, text="Temperature (T)", padding="5")
        self.t_sub_frame.grid(row=3, column=0, columnspan=4, padx=5, pady=5, sticky=(tk.W, tk.E))

        ttk.Label(self.t_sub_frame, text="Mode:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Combobox(self.t_sub_frame, textvariable=self.t_mode_var, values=["Static", "Ramp", "Random"], state="readonly", width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.t_sub_frame, text="Static (C):").grid(row=0, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_static_var, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.t_sub_frame, text="Min (C):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_min_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.t_sub_frame, text="Max (C):").grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_max_var, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)

        ttk.Label(self.t_sub_frame, text="Ramp Start (C):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_ramp_start_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.t_sub_frame, text="Ramp End (C):").grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_ramp_end_var, width=10).grid(row=2, column=3, sticky=tk.W, padx=5, pady=2)
        ttk.Label(self.t_sub_frame, text="Ramp Duration (s):").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.t_sub_frame, textvariable=self.t_ramp_duration_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)


    def create_output_config_frame(self, parent_frame):
        output_frame = ttk.LabelFrame(parent_frame, text="Output Configuration", padding="10")
        output_frame.grid(row=1, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(output_frame, text="Data Format:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        output_formats = ["Valeport Standard", "Alt #2", "Alt #3", "AML SVT", "MVP"]
        ttk.Combobox(output_frame, textvariable=self.data_format_var, values=output_formats, state="readonly", width=20).grid(row=0, column=1, columnspan=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(output_frame, text="Protocol:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        protocol_combo = ttk.Combobox(output_frame, textvariable=self.protocol_var, values=["UDP", "TCP"], state="readonly", width=10)
        protocol_combo.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        protocol_combo.bind("<<ComboboxSelected>>", self.on_protocol_change)

        ttk.Label(output_frame, text="IP Address:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.ip_entry = ttk.Entry(output_frame, textvariable=self.ip_address_var, width=20)
        self.ip_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        self.udp_broadcast_check = ttk.Checkbutton(output_frame, text="UDP Broadcast", variable=self.udp_broadcast_var, command=self.on_udp_broadcast_toggle)
        self.udp_broadcast_check.grid(row=2, column=2, sticky=tk.W, padx=5, pady=2)

        ttk.Label(output_frame, text="Port:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(output_frame, textvariable=self.port_var, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)

        ttk.Label(output_frame, text="Update Rate (Hz):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Combobox(output_frame, textvariable=self.update_rate_var, values=["1", "2", "4", "8"], state="readonly", width=10).grid(row=4, column=1, sticky=tk.W, padx=5, pady=2)

    def create_control_status_frame(self, parent_frame):
        control_frame = ttk.LabelFrame(parent_frame, text="Simulation Control & Status", padding="10")
        control_frame.grid(row=2, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(pady=5)

        self.start_button = ttk.Button(button_frame, text="START SIMULATION", command=self.start_simulation, width=20)
        self.start_button.pack(side=tk.LEFT, padx=5)
        self.stop_button = ttk.Button(button_frame, text="STOP SIMULATION", command=self.stop_simulation, state=tk.DISABLED, width=20)
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Label(control_frame, textvariable=self.status_var).pack(pady=2, anchor=tk.W)
        ttk.Label(control_frame, textvariable=self.tcp_clients_var).pack(pady=2, anchor=tk.W)
        
        ttk.Label(control_frame, text="Last Output String:").pack(pady=2, anchor=tk.W)
        self.last_output_text = scrolledtext.ScrolledText(control_frame, height=3, width=60, wrap=tk.WORD, state=tk.DISABLED)
        self.last_output_text.pack(pady=5, fill=tk.X, expand=True)

    # --- Event Handlers & Callbacks ---
    def on_sensor_type_change(self, event=None):
        sensor_type = self.sensor_type_var.get()
        if sensor_type == "SV only":
            for child in self.p_sub_frame.winfo_children(): child.configure(state=tk.DISABLED)
            for child in self.t_sub_frame.winfo_children(): child.configure(state=tk.DISABLED)
        elif sensor_type == "SV+P":
            for child in self.p_sub_frame.winfo_children(): child.configure(state=tk.NORMAL)
            for child in self.t_sub_frame.winfo_children(): child.configure(state=tk.DISABLED)
        elif sensor_type == "SV+T":
            for child in self.p_sub_frame.winfo_children(): child.configure(state=tk.DISABLED)
            for child in self.t_sub_frame.winfo_children(): child.configure(state=tk.NORMAL)
        self.on_pressure_fs_change() # Update pressure format label based on FS, even if P disabled

    def on_pressure_fs_change(self, event=None):
        try:
            fs = int(self.p_fs_var.get())
            if fs <= 99: # Hypothetical, not in current dropdown
                 self.p_format_label_var.set("PP.PPP")
            elif fs <= 500: # Covers 100, 500
                 self.p_format_label_var.set("PPP.PP")
            else: # Covers 1000, 6000
                 self.p_format_label_var.set("PPPP.P")
        except ValueError:
            self.p_format_label_var.set("Error")

    def on_protocol_change(self, event=None):
        protocol = self.protocol_var.get()
        if protocol == "UDP":
            self.udp_broadcast_check.config(state=tk.NORMAL)
            self.on_udp_broadcast_toggle() # Update IP entry based on broadcast state
        else: # TCP
            self.udp_broadcast_check.config(state=tk.DISABLED)
            self.ip_entry.config(state=tk.NORMAL)
            self.ip_address_var.set("0.0.0.0") # Default listen on all interfaces for TCP server

    def on_udp_broadcast_toggle(self):
        if self.protocol_var.get() == "UDP":
            if self.udp_broadcast_var.get():
                self.ip_entry.config(state=tk.DISABLED)
                # self.ip_address_var.set("***************") # Or <broadcast> for some systems
            else:
                self.ip_entry.config(state=tk.NORMAL)
                if self.ip_address_var.get() == "***************" or self.ip_address_var.get() == "<broadcast>":
                    self.ip_address_var.set("127.0.0.1") # Default back to localhost if disabling broadcast

    # --- Simulation Logic ---
    def start_simulation(self):
        if self.simulation_running:
            self.update_status("Simulation already running.", is_error=True)
            return

        try:
            # Validate inputs (basic validation)
            self.port = int(self.port_var.get())
            if not (1024 <= self.port <= 65535):
                raise ValueError("Port must be between 1024 and 65535.")
            
            self.ip = self.ip_address_var.get()
            if self.protocol_var.get() == "UDP" and self.udp_broadcast_var.get():
                self.ip = '<broadcast>' # Special address for broadcast
            elif self.protocol_var.get() == "TCP":
                 self.ip = self.ip_address_var.get() # Should be 0.0.0.0 or specific IP to listen on
            # Add more validation for numeric fields if needed

        except ValueError as e:
            self.update_status(f"Configuration error: {e}", is_error=True)
            messagebox.showerror("Input Error", str(e))
            return

        self.simulation_running = True
        self.update_status("Simulation starting...")
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.disable_config_widgets()

        # Initialize ramp start times and values
        self.ramp_sv_start_time = time.time()
        self.ramp_sv_initial_value = float(self.sv_ramp_start_var.get())
        self.ramp_p_start_time = time.time()
        self.ramp_p_initial_value = self._get_pressure_input_as_dbar(self.p_ramp_start_var.get())
        self.ramp_t_start_time = time.time()
        self.ramp_t_initial_value = float(self.t_ramp_start_var.get())

        if self.protocol_var.get() == "TCP":
            self.tcp_client_sockets = []
            self.update_tcp_client_count()
            self.tcp_server_thread = threading.Thread(target=self.tcp_server_loop, daemon=True)
            self.tcp_server_thread.start()
        
        self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
        self.simulation_thread.start()

    def stop_simulation(self):
        if not self.simulation_running:
            return

        self.simulation_running = False
        if self.simulation_thread and self.simulation_thread.is_alive():
            # self.simulation_thread.join() # Let it finish its current iteration
            pass # It will exit based on self.simulation_running

        if self.tcp_server_socket:
            try:
                self.tcp_server_socket.close() # This should break the accept loop
            except OSError:
                pass # Socket might already be closed
            self.tcp_server_socket = None
        
        # if self.tcp_accept_thread and self.tcp_accept_thread.is_alive():
        #     self.tcp_accept_thread.join(timeout=1) # Wait briefly

        for sock in self.tcp_client_sockets:
            try:
                sock.close()
            except OSError:
                pass
        self.tcp_client_sockets = []
        self.update_tcp_client_count()
        
        self.update_status("Simulation stopped.")
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.enable_config_widgets()

    def disable_config_widgets(self):
        for child in self.master.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                for widget in child.winfo_children():
                    self._set_widget_state_recursive(widget, tk.DISABLED)
        # Re-enable control buttons explicitly if needed, but start/stop are handled separately
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)


    def enable_config_widgets(self):
        for child in self.master.winfo_children():
            if isinstance(child, ttk.LabelFrame):
                for widget in child.winfo_children():
                     self._set_widget_state_recursive(widget, tk.NORMAL)
        # Special handling for some widgets after re-enabling
        self.on_sensor_type_change() # Re-apply disabled states for P/T sections
        self.on_protocol_change()    # Re-apply IP entry state
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)


    def _set_widget_state_recursive(self, widget, state):
        try:
            widget.configure(state=state)
        except tk.TclError: # Some widgets like LabelFrame itself don't have 'state'
            pass
        if hasattr(widget, 'winfo_children'):
            for child in widget.winfo_children():
                self._set_widget_state_recursive(child, state)


    def simulation_loop(self):
        udp_socket = None
        if self.protocol_var.get() == "UDP":
            try:
                udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                if self.udp_broadcast_var.get():
                    udp_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            except socket.error as e:
                self.update_status(f"UDP Socket Error: {e}", is_error=True)
                self.stop_simulation() # Stop if socket fails
                return
        
        update_interval = 1.0 / float(self.update_rate_var.get())

        while self.simulation_running:
            start_time = time.time()
            
            sv_val = self.generate_sv_value()
            p_val_dbar = self.generate_pressure_value() if self.sensor_type_var.get() in ["SV+P"] else None
            t_val = self.generate_temperature_value() if self.sensor_type_var.get() in ["SV+T"] else None
            
            data_string = self.format_data_string(sv_val, p_val_dbar, t_val)
            self.update_last_output(data_string)

            if self.protocol_var.get() == "UDP" and udp_socket:
                try:
                    target_ip = self.ip if self.udp_broadcast_var.get() else self.ip_address_var.get()
                    udp_socket.sendto(data_string.encode('ascii'), (target_ip, self.port))
                except socket.error as e:
                    self.update_status(f"UDP Send Error: {e}", is_error=True)
                    # Optionally stop simulation or just log error
            elif self.protocol_var.get() == "TCP":
                self.broadcast_to_tcp_clients(data_string)

            elapsed_time = time.time() - start_time
            sleep_time = update_interval - elapsed_time
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        if udp_socket:
            udp_socket.close()
        self.update_status("Simulation loop finished.")


    def _get_pressure_input_as_dbar(self, p_str_val):
        val = float(p_str_val)
        unit = self.p_unit_var.get()
        if unit == "m":
            return val * DBAR_PER_METER
        elif unit == "ft":
            return val * DBAR_PER_FOOT
        return val # dBar

    def generate_sv_value(self):
        mode = self.sv_mode_var.get()
        try:
            if mode == "Static":
                return float(self.sv_static_var.get())
            elif mode == "Random":
                min_val = float(self.sv_min_var.get())
                max_val = float(self.sv_max_var.get())
                return round(random.uniform(min_val, max_val), 3)
            elif mode == "Ramp":
                duration = float(self.sv_ramp_duration_var.get())
                target = float(self.sv_ramp_end_var.get())
                elapsed = time.time() - self.ramp_sv_start_time
                if duration <= 0 or elapsed >= duration:
                    return target
                progress = elapsed / duration
                return self.ramp_sv_initial_value + progress * (target - self.ramp_sv_initial_value)
        except ValueError:
            return 1500.0 # Default fallback

    def generate_pressure_value(self): # Returns dBar
        mode = self.p_mode_var.get()
        try:
            if mode == "Static":
                return self._get_pressure_input_as_dbar(self.p_static_var.get())
            elif mode == "Random":
                min_val_dbar = self._get_pressure_input_as_dbar(self.p_min_var.get())
                max_val_dbar = self._get_pressure_input_as_dbar(self.p_max_var.get())
                fs_dbar = float(self.p_fs_var.get())
                resolution = 0.0001 * fs_dbar # 0.01% FS
                val = random.uniform(min_val_dbar, max_val_dbar)
                return round(val / resolution) * resolution if resolution > 0 else val
            elif mode == "Ramp":
                duration = float(self.p_ramp_duration_var.get())
                target_dbar = self._get_pressure_input_as_dbar(self.p_ramp_end_var.get())
                elapsed = time.time() - self.ramp_p_start_time
                if duration <= 0 or elapsed >= duration:
                    return target_dbar
                progress = elapsed / duration
                return self.ramp_p_initial_value + progress * (target_dbar - self.ramp_p_initial_value)
        except ValueError:
             return 0.5 # Default fallback dBar

    def generate_temperature_value(self):
        mode = self.t_mode_var.get()
        try:
            if mode == "Static":
                return float(self.t_static_var.get())
            elif mode == "Random":
                min_val = float(self.t_min_var.get())
                max_val = float(self.t_max_var.get())
                return round(random.uniform(min_val, max_val), 3)
            elif mode == "Ramp":
                duration = float(self.t_ramp_duration_var.get())
                target = float(self.t_ramp_end_var.get())
                elapsed = time.time() - self.ramp_t_start_time
                if duration <= 0 or elapsed >= duration:
                    return target
                progress = elapsed / duration
                return self.ramp_t_initial_value + progress * (target - self.ramp_t_initial_value)
        except ValueError:
            return 18.0 # Default fallback

    def get_pressure_string(self, p_dbar_val):
        if p_dbar_val is None:
            return ""
        try:
            fs = int(self.p_fs_var.get())
            if fs <= 99: # e.g. PP.PPP for 100dbar in wireframe, let's use 6.3 for consistency
                # For 100dBar, wireframe implies PP.PPP, manual example 12.345 (6 chars)
                # Let's use the format string that produces 6 chars.
                # For 100 FS, resolution 0.01. Format PPP.PP (e.g. 012.34) is better.
                # Based on wireframe default for 100 FS: PP.PPP -> {:06.3f}
                # Based on manual examples for length 6:
                # PPPP.P (1234.5) -> {:06.1f}
                # PPP.PP (123.45) -> {:06.2f}
                # PP.PPP (12.345) -> {:06.3f}
                # Using the auto-label from on_pressure_fs_change:
                fmt_str = self.p_format_label_var.get()
                if fmt_str == "PP.PPP":
                    return f"{p_dbar_val:06.3f}"
                elif fmt_str == "PPP.PP":
                    return f"{p_dbar_val:06.2f}"
                elif fmt_str == "PPPP.P":
                    return f"{p_dbar_val:06.1f}"
                else: # Fallback
                    return f"{p_dbar_val:06.2f}" # Default to PPP.PP
            elif fs <= 500: # Covers 100, 500
                 return f"{p_dbar_val:06.2f}" # e.g., 123.45 or 012.34
            else: # Covers 1000, 6000
                 return f"{p_dbar_val:06.1f}" # e.g., 1234.5 or 0123.4
        except ValueError:
            return "ERR.P"


    def get_temperature_string(self, t_val):
        if t_val is None:
            return ""
        # Manual: "fixed to a 5 digit string with 3 decimal places... signed only if negative."
        # Examples: 21.456, 02.769, -01.174 (7 chars with sign, 6 without)
        if t_val < 0:
            return f"{t_val:07.3f}" # e.g., -02.123
        else:
            return f"{t_val:06.3f}" # e.g., 02.123 or 21.123

    def format_data_string(self, sv_val_mps, p_val_dbar, t_val_c):
        # Common processing
        p_str = self.get_pressure_string(p_val_dbar) if p_val_dbar is not None else "000.00" # Default if P not active
        t_str = self.get_temperature_string(t_val_c) if t_val_c is not None else "00.000" # Default if T not active
        
        # Sensor type specific handling for placeholders
        sensor_type = self.sensor_type_var.get()
        
        # For formats that might not include P or T, use defaults or empty strings
        # if sensor_type == "SV only", p_str and t_str will be their defaults above.
        # if sensor_type == "SV+P", t_str will be its default.
        # if sensor_type == "SV+T", p_str will be its default.

        fmt = self.data_format_var.get()
        output = ""

        if fmt == "Valeport Standard": # <space>{pressure}<space>{temperature}<space>SV_mmps<cr><lf>
            sv_mmps = int(round(sv_val_mps * 1000))
            # Pressure and Temp strings are based on whether they are active
            _p = p_str if sensor_type == "SV+P" else (" " * len(p_str)) # Keep spacing if not active
            _t = t_str if sensor_type == "SV+T" else (" " * len(t_str))

            if sensor_type == "SV only":
                 output = f" {sv_mmps:7d}"
            elif sensor_type == "SV+P":
                 output = f" {p_str} {sv_mmps:7d}"
            elif sensor_type == "SV+T":
                 output = f" {t_str} {sv_mmps:7d}"
            # Note: The manual doesn't explicitly show SV+P+T for this format, but if it existed:
            # output = f" {p_str} {t_str} {sv_mmps:7d}"

        elif fmt == "Alt #2": # <space>{pressure}<space>{temperature}<space>SV_mps_2dp<cr><lf>
            sv_str = f"{sv_val_mps:06.2f}" # e.g. 1500.12
            if sensor_type == "SV only":
                 output = f" {sv_str}"
            elif sensor_type == "SV+P":
                 output = f" {p_str} {sv_str}"
            elif sensor_type == "SV+T":
                 output = f" {t_str} {sv_str}"

        elif fmt == "Alt #3": # <space>{pressure}<space>{temperature}<space>SV_mps_3dp<cr><lf>
            sv_str = f"{sv_val_mps:07.3f}" # e.g. 1500.123
            if sensor_type == "SV only":
                 output = f" {sv_str}"
            elif sensor_type == "SV+P":
                 output = f" {p_str} {sv_str}"
            elif sensor_type == "SV+T":
                 output = f" {t_str} {sv_str}"
        
        elif fmt == "AML SVT": # <space>{temperature}<space><space>SV_mps_3dp<space><space><cr><lf>
            # Only T and SV. If T is not active, substitute zeroes (or default t_str)
            _t_for_aml = t_str if sensor_type == "SV+T" else "00.000"
            sv_str = f"{sv_val_mps:07.3f}" # e.g. 1500.123
            output = f" {_t_for_aml}  {sv_str}  "

        elif fmt == "MVP": # <space>pppp.p<space><space>ssss.ss<space><space>tt.ttt<cr><lf>
            # This format has its own fixed field formats
            # Pressure: pppp.p (6 chars)
            _p_mvp = f"{p_val_dbar:06.1f}" if p_val_dbar is not None and sensor_type == "SV+P" else "0000.0"
            # SV: ssss.ss (7 chars)
            _sv_mvp = f"{sv_val_mps:07.2f}" # e.g. 1500.12
            # Temp: tt.ttt (6 chars for positive, 7 for negative)
            _t_mvp = self.get_temperature_string(t_val_c) if t_val_c is not None and sensor_type == "SV+T" else "+00.000"
            if not _t_mvp.startswith('-') and not _t_mvp.startswith('+'): # ensure sign for positive if get_temperature_string doesn't add it
                _t_mvp = f"+{_t_mvp}" if float(_t_mvp) >= 0 else _t_mvp
            if len(_t_mvp) == 6 and _t_mvp[0] != '-': # Positive, needs sign
                _t_mvp = f"+{_t_mvp}"
            
            # Ensure correct lengths for MVP temp if get_temperature_string was used
            # MVP tt.ttt implies fixed length. Let's reformat for MVP specifically.
            if t_val_c is not None and sensor_type == "SV+T":
                if t_val_c < 0:
                    _t_mvp = f"{t_val_c:07.3f}" # e.g. -02.123
                else:
                    _t_mvp = f"{t_val_c:06.3f}" # e.g. 02.123 or 18.123. MVP needs sign, so add +
                    _t_mvp = f"+{_t_mvp}"
            else:
                _t_mvp = "+00.000"


            output = f" {_p_mvp}  {_sv_mvp}  {_t_mvp} "
            
        return output.rstrip() + "\r\n"


    # --- Networking Logic ---
    def tcp_server_loop(self):
        try:
            self.tcp_server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.tcp_server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            listen_ip = self.ip_address_var.get() if self.protocol_var.get() == "TCP" else "0.0.0.0"
            self.tcp_server_socket.bind((listen_ip, self.port))
            self.tcp_server_socket.listen(5)
            self.tcp_server_socket.settimeout(1.0) # Timeout for accept
            self.update_status(f"TCP Server listening on {listen_ip}:{self.port}")

            while self.simulation_running and self.protocol_var.get() == "TCP":
                try:
                    client_socket, addr = self.tcp_server_socket.accept()
                    client_socket.setblocking(True) # Or False if handling send errors better
                    self.tcp_client_sockets.append(client_socket)
                    self.update_tcp_client_count()
                    self.update_status(f"TCP Client connected: {addr}")
                except socket.timeout:
                    continue # Check self.simulation_running again
                except OSError: # Socket closed by stop_simulation
                    if self.simulation_running: # Unexpected close
                        self.update_status("TCP Server socket closed unexpectedly.", is_error=True)
                    break 
            
        except socket.error as e:
            self.update_status(f"TCP Server Error: {e}", is_error=True)
        finally:
            if self.tcp_server_socket:
                self.tcp_server_socket.close()
            self.tcp_server_socket = None # Ensure it's cleared
            if self.simulation_running : # If loop exited but sim should be running
                 self.update_status("TCP Server stopped.", is_error=self.simulation_running)


    def broadcast_to_tcp_clients(self, data_string):
        # Iterate on a copy of the list in case of modifications
        for client_socket in list(self.tcp_client_sockets):
            try:
                client_socket.sendall(data_string.encode('ascii'))
            except socket.error:
                try:
                    self.tcp_client_sockets.remove(client_socket)
                    client_socket.close()
                except (ValueError, OSError): # Already removed or closed
                    pass
                self.update_tcp_client_count()
                self.update_status("TCP Client disconnected.")


    # --- Utility Methods ---
    def update_status(self, message, is_error=False):
        self.gui_queue.put(("status", message, is_error))

    def _update_status_gui(self, message, is_error):
        self.status_var.set(message)
        # Could add color change for errors if desired

    def update_last_output(self, data_string):
        self.gui_queue.put(("last_output", data_string))
        
    def _update_last_output_gui(self, data_string):
        self.last_output_text.config(state=tk.NORMAL)
        self.last_output_text.delete(1.0, tk.END)
        self.last_output_text.insert(tk.END, data_string)
        self.last_output_text.see(tk.END) # Scroll to end
        self.last_output_text.config(state=tk.DISABLED)

    def update_tcp_client_count(self):
        self.gui_queue.put(("tcp_clients", len(self.tcp_client_sockets)))

    def _update_tcp_client_count_gui(self, count):
        self.tcp_clients_var.set(f"Connected Clients (TCP): {count}")

    def check_gui_queue(self):
        try:
            while True:
                message_type, *args = self.gui_queue.get_nowait()
                if message_type == "status":
                    self._update_status_gui(*args)
                elif message_type == "last_output":
                    self._update_last_output_gui(*args)
                elif message_type == "tcp_clients":
                    self._update_tcp_client_count_gui(*args)
        except queue.Empty:
            pass
        finally:
            self.master.after(100, self.check_gui_queue) # Poll every 100ms

    def on_closing(self):
        if self.simulation_running:
            if messagebox.askokcancel("Quit", "Simulation is running. Do you want to stop and quit?"):
                self.stop_simulation()
                # Wait a bit for threads to clean up before destroying window
                self.master.after(500, self.master.destroy)
            else:
                return # Don't close
        else:
            self.master.destroy()


if __name__ == "__main__":
    root = tk.Tk()
    app = ValeportMiniSVSSimulator(root)
    root.mainloop()
