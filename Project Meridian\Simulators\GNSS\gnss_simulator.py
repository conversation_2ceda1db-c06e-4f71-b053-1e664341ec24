import sys
import datetime
import math
import time
import socket
import threading # For TCP server accept loop

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QCheckBox, QPlainTextEdit,
    QGroupBox, QRadioButton, QMessageBox
)
from PyQt6.QtCore import QTimer, Qt, QCoreApplication

# --- Constants (largely unchanged) ---
KNOTS_TO_MPS = 0.514444
MPS_TO_KNOTS = 1.0 / KNOTS_TO_MPS
EARTH_RADIUS_METERS = 6371000
DEFAULT_TURN_RATE_DPS = 5.0
DEFAULT_WAYPOINT_PROXIMITY_METERS = 50

# --- NMEA Utilities (unchanged from previous version) ---
def calculate_nmea_checksum(sentence_without_checksum):
    if sentence_without_checksum.startswith('$'):
        sentence_without_checksum = sentence_without_checksum[1:]
    if '*' in sentence_without_checksum:
        sentence_without_checksum = sentence_without_checksum.split('*')[0]
    checksum = 0
    for char in sentence_without_checksum:
        checksum ^= ord(char)
    return format(checksum, '02X')

def format_time_utc(dt_object: datetime.datetime):
    return dt_object.strftime("%H%M") + f"{dt_object.second + dt_object.microsecond / 1_000_000.0:05.2f}"

def format_date_utc(dt_object: datetime.datetime):
    return dt_object.strftime("%d%m%y")

def format_latitude_nmea(latitude):
    if latitude is None: return ",,"
    hemisphere = 'N' if latitude >= 0 else 'S'
    latitude = abs(latitude)
    degrees = int(latitude)
    minutes = (latitude - degrees) * 60
    return f"{degrees:02d}{minutes:07.4f},{hemisphere}"

def format_longitude_nmea(longitude):
    if longitude is None: return ",,"
    hemisphere = 'E' if longitude >= 0 else 'W'
    longitude = abs(longitude)
    degrees = int(longitude)
    minutes = (longitude - degrees) * 60
    return f"{degrees:03d}{minutes:07.4f},{hemisphere}"

def haversine_distance(lat1, lon1, lat2, lon2):
    R = EARTH_RADIUS_METERS
    lat1_rad, lon1_rad = math.radians(lat1), math.radians(lon1)
    lat2_rad, lon2_rad = math.radians(lat2), math.radians(lon2)
    dlon = lon2_rad - lon1_rad
    dlat = lat2_rad - lat1_rad
    a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    return R * c

def bearing_to_waypoint(current_lat, current_lon, target_lat, target_lon):
    lat1, lon1 = math.radians(current_lat), math.radians(current_lon)
    lat2, lon2 = math.radians(target_lat), math.radians(target_lon)
    dlon = lon2 - lon1
    x = math.sin(dlon) * math.cos(lat2)
    y = math.cos(lat1) * math.sin(lat2) - (math.sin(lat1) * math.cos(lat2) * math.cos(dlon))
    return (math.degrees(math.atan2(x, y)) + 360) % 360

def normalize_angle_diff(diff):
    while diff <= -180: diff += 360
    while diff > 180: diff -= 360
    return diff

# --- GST Profiles (unchanged) ---
GST_PROFILES = {
    0: (99.9, 99.9, 99.9, 0.0, 99.9, 99.9, 99.9),
    1: (10.0, 8.0, 6.0, 45.0, 7.5, 5.5, 12.0),
    2: (2.5, 2.0, 1.5, 30.0, 1.8, 1.2, 3.0),
    5: (0.8, 0.6, 0.4, 60.0, 0.5, 0.3, 1.0),
    4: (0.05, 0.03, 0.02, 90.0, 0.025, 0.015, 0.04)
}

# --- Path Controller Class (unchanged) ---
class PathController:
    def __init__(self, sim_state):
        self.sim_state = sim_state
        self.preset_paths = {
            "North Sea Survey Grid": [
                {"type": "start", "lat": 54.0, "lon": 2.0, "alt": 25.0, "heading": 0.0, "speed_knots": 0.0},
                {"type": "waypoint", "lat": 54.0, "lon": 2.0, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 54.2, "lon": 2.0, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 54.2, "lon": 2.05, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 54.0, "lon": 2.05, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 54.0, "lon": 2.10, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 54.2, "lon": 2.10, "speed_knots": 6.0},
                {"type": "end"},
            ],
            "Port Approach (Example)": [
                {"type": "start", "lat": 33.70, "lon": -118.26, "alt": 15.0, "heading": 180.0, "speed_knots": 0.0},
                {"type": "waypoint", "lat": 33.70, "lon": -118.26, "speed_knots": 8.0}, # LA Harbor Area
                {"type": "waypoint", "lat": 33.72, "lon": -118.26, "speed_knots": 8.0},
                {"type": "waypoint", "lat": 33.73, "lon": -118.25, "speed_knots": 6.0},
                {"type": "waypoint", "lat": 33.74, "lon": -118.24, "speed_knots": 4.0},
                {"type": "end"},
            ],
            "Coastal Mapping Leg (Example)": [
                {"type": "start", "lat": 36.5, "lon": -122.0, "alt": 20.0, "heading": 330.0, "speed_knots": 0.0},
                {"type": "waypoint", "lat": 36.5, "lon": -122.0, "speed_knots": 10.0}, # Monterey Bay area
                {"type": "waypoint", "lat": 36.8, "lon": -122.15, "speed_knots": 10.0},
                {"type": "waypoint", "lat": 37.1, "lon": -122.30, "speed_knots": 10.0},
                {"type": "end"},
            ],
            "Still (London)": [
                 {"type": "start", "lat": 51.5074, "lon": -0.1278, "alt": 50.0, "heading": 0.0, "speed_knots": 0.0},
                 {"type": "waypoint", "lat": 51.5074, "lon": -0.1278, "speed_knots": 0.0},
                 {"type": "end"},
            ]
        }
        self.current_path_name = None
        self.current_path_definition = []
        self.current_waypoint_index = 0
        self.target_heading = 0.0
        self.target_speed_knots = 0.0
        self.is_maneuvering = False

    def load_path(self, path_name):
        if path_name in self.preset_paths:
            self.current_path_name = path_name
            self.current_path_definition = self.preset_paths[path_name]
            self.current_waypoint_index = 0
            self._apply_start_conditions()
            self.sim_state.on_path = True
            self.sim_state.log_message_signal.emit(f"Path loaded: {path_name}")
            return True
        self.sim_state.log_message_signal.emit(f"Error: Path '{path_name}' not found.")
        self.sim_state.on_path = False
        return False

    def _apply_start_conditions(self):
        if self.current_path_definition and self.current_path_definition[0]["type"] == "start":
            start_config = self.current_path_definition[0]
            self.sim_state.latitude = start_config["lat"]
            self.sim_state.longitude = start_config["lon"]
            self.sim_state.altitude_msl = start_config["alt"]
            self.sim_state.heading_true = start_config["heading"]
            self.sim_state.speed_knots = start_config["speed_knots"]
            self.target_heading = start_config["heading"]
            self.target_speed_knots = start_config["speed_knots"]
            self.current_waypoint_index = 1
            self.sim_state.log_message_signal.emit(f"Applied start conditions: Lat={self.sim_state.latitude:.4f}, Lon={self.sim_state.longitude:.4f}, Hdg={self.sim_state.heading_true:.1f}")

    def update(self, delta_time_seconds):
        if not self.sim_state.on_path or not self.current_path_definition or \
           self.current_waypoint_index >= len(self.current_path_definition):
            if self.sim_state.on_path and self.current_path_definition and self.current_waypoint_index >= len(self.current_path_definition):
                self.sim_state.log_message_signal.emit(f"Path '{self.current_path_name}' finished.")
            self.sim_state.on_path = False
            return

        current_segment = self.current_path_definition[self.current_waypoint_index]

        if current_segment["type"] == "end":
            self.sim_state.log_message_signal.emit(f"Reached end of path '{self.current_path_name}'.")
            self.sim_state.on_path = False
            return

        if current_segment["type"] == "waypoint":
            target_wp_lat = current_segment["lat"]
            target_wp_lon = current_segment["lon"]
            self.target_speed_knots = current_segment.get("speed_knots", self.sim_state.speed_knots)

            distance_to_wp = haversine_distance(
                self.sim_state.latitude, self.sim_state.longitude,
                target_wp_lat, target_wp_lon
            )

            if distance_to_wp < current_segment.get("proximity_m", DEFAULT_WAYPOINT_PROXIMITY_METERS):
                self.sim_state.log_message_signal.emit(f"Reached waypoint {self.current_waypoint_index}: Lat={target_wp_lat:.4f}, Lon={target_wp_lon:.4f}")
                self.current_waypoint_index += 1
                self.is_maneuvering = True
                if self.current_waypoint_index >= len(self.current_path_definition) or \
                   self.current_path_definition[self.current_waypoint_index]["type"] == "end":
                    self.sim_state.on_path = False
                    self.sim_state.log_message_signal.emit(f"Path '{self.current_path_name}' processing complete.")
                return

            self.sim_state.speed_knots = self.target_speed_knots

            if self.sim_state.speed_knots > 0.1:
                self.target_heading = bearing_to_waypoint(
                    self.sim_state.latitude, self.sim_state.longitude,
                    target_wp_lat, target_wp_lon
                )
                heading_diff = normalize_angle_diff(self.target_heading - self.sim_state.heading_true)
                turn_this_step = self.sim_state.turn_rate_dps * delta_time_seconds

                if abs(heading_diff) < 0.1:
                     self.sim_state.heading_true = self.target_heading
                     self.is_maneuvering = False
                elif abs(heading_diff) <= turn_this_step:
                    self.sim_state.heading_true = self.target_heading
                    self.is_maneuvering = False
                else:
                    self.sim_state.heading_true += math.copysign(turn_this_step, heading_diff)
                    self.is_maneuvering = True
                self.sim_state.heading_true = (self.sim_state.heading_true + 360) % 360
            else:
                self.is_maneuvering = False
        else:
            self.current_waypoint_index += 1


# --- GNSS Simulator State Class (minor additions for GUI signals) ---
class GNSSSimulatorState(QWidget): # Inherit QWidget for signals
    # Signal to send log messages to the GUI
    log_message_signal = pyqtSignal(str) if 'pyqtSignal' in globals() else None # Conditional for testing

    def __init__(self):
        super().__init__() # Needed for signals
        self.current_time_utc = datetime.datetime.now(datetime.timezone.utc)
        self.latitude = 51.5074
        self.longitude = -0.1278
        self.altitude_msl = 50.0
        self.geoid_separation = -5.3

        self.speed_knots = 0.0
        self.heading_true = 0.0
        self.turn_rate_dps = DEFAULT_TURN_RATE_DPS

        self.fix_quality = 1 # 0=Invalid, 1=SPS, 2=DGPS/SBAS, 4=RTK Fixed, 5=RTK Float
        self.num_satellites = 8
        self.hdop = 1.0
        self.vdop = 1.5
        self.pdop = 1.8

        self.mode_gsa_1 = 'A'
        self.satellite_prns = ['01', '04', '07', '10', '13', '16', '19', '22']
        self.gst_profile = GST_PROFILES[self.fix_quality]
        self.last_update_sim_time = time.monotonic()

        self.simulation_running = False
        self.output_mode = "UDP" # "UDP" or "TCP"
        self.udp_ip = "127.0.0.1"
        self.udp_port = 10110
        self.tcp_ip = "0.0.0.0" # Listen on all interfaces for TCP server
        self.tcp_port = 10110
        self.update_rate_hz = 1.0

        self.simulation_mode = "manual" # "manual" or "preset_path"
        self.on_path = False

        self.manual_target_speed_knots = 0.0
        self.manual_target_heading_deg = 0.0
        self.manual_rate_of_turn_dps = 0.0

        # For occasional errors (future use)
        self.simulate_dropout = False
        self.dropout_timer = 0
        self.simulate_cycle_slip = False


    def update_simulation(self):
        current_sim_time = time.monotonic()
        delta_time_seconds = current_sim_time - self.last_update_sim_time
        if delta_time_seconds <= 0: # Ensure positive delta time
            delta_time_seconds = 1.0 / (self.update_rate_hz * 10.0) if self.update_rate_hz > 0 else 0.01

        self.last_update_sim_time = current_sim_time
        self.update_time()

        if self.simulation_mode == "manual":
            self.on_path = False
            self.speed_knots = self.manual_target_speed_knots
            if self.manual_rate_of_turn_dps != 0:
                self.heading_true += self.manual_rate_of_turn_dps * delta_time_seconds
            else:
                heading_diff = normalize_angle_diff(self.manual_target_heading_deg - self.heading_true)
                turn_this_step = self.turn_rate_dps * delta_time_seconds
                if abs(heading_diff) <= turn_this_step:
                    self.heading_true = self.manual_target_heading_deg
                elif abs(heading_diff) > 0.01:
                    self.heading_true += math.copysign(turn_this_step, heading_diff)
            self.heading_true = (self.heading_true + 360) % 360
        # Path controller updates speed/heading directly on sim_state if in "preset_path" mode

        self.update_position(delta_time_seconds)
        self.gst_profile = GST_PROFILES.get(self.fix_quality, GST_PROFILES[0])
        base_prns = [f"{i:02d}" for i in range(1, 33)]
        self.satellite_prns = base_prns[:max(0, min(len(base_prns), self.num_satellites))]


    def update_time(self):
        self.current_time_utc = datetime.datetime.now(datetime.timezone.utc)

    def update_position(self, delta_time_seconds):
        if self.speed_knots < 0.01 and (self.simulation_mode == "manual" and self.manual_rate_of_turn_dps == 0):
            return

        distance_meters = self.speed_knots * KNOTS_TO_MPS * delta_time_seconds
        heading_rad = math.radians(self.heading_true)
        lat_rad = math.radians(self.latitude)
        lon_rad = math.radians(self.longitude)

        if distance_meters > 0:
            new_lat_rad = math.asin(math.sin(lat_rad) * math.cos(distance_meters / EARTH_RADIUS_METERS) +
                                    math.cos(lat_rad) * math.sin(distance_meters / EARTH_RADIUS_METERS) * math.cos(heading_rad))
            new_lon_rad = lon_rad + math.atan2(math.sin(heading_rad) * math.sin(distance_meters / EARTH_RADIUS_METERS) * math.cos(lat_rad),
                                            math.cos(distance_meters / EARTH_RADIUS_METERS) - math.sin(lat_rad) * math.sin(new_lat_rad))
            self.latitude = math.degrees(new_lat_rad)
            self.longitude = math.degrees(new_lon_rad)
            self.longitude = (self.longitude + 180) % 360 - 180

    def set_start_position(self, lat, lon, alt, heading):
        self.latitude = lat
        self.longitude = lon
        self.altitude_msl = alt
        self.heading_true = heading
        if self.simulation_mode == "manual":
            self.manual_target_heading_deg = heading
        if self.log_message_signal:
            self.log_message_signal.emit(f"Manual start position: Lat={lat:.4f}, Lon={lon:.4f}, Hdg={heading:.1f}")

# --- NMEA Sentence Generators (largely unchanged, ensure they use the state object) ---
def create_gpgga(state: GNSSSimulatorState):
    sentence = "GPGGA,"
    sentence += f"{format_time_utc(state.current_time_utc)},"
    sentence += f"{format_latitude_nmea(state.latitude)},"
    sentence += f"{format_longitude_nmea(state.longitude)},"
    sentence += f"{state.fix_quality},"
    sentence += f"{state.num_satellites:02d},"
    sentence += f"{state.hdop:.1f},"
    sentence += f"{state.altitude_msl:.1f},M,"
    sentence += f"{state.geoid_separation:.1f},M,"
    sentence += "," # Age DGPS
    sentence += ""  # DGPS Stn ID
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gphdt(state: GNSSSimulatorState):
    sentence = "GPHDT,"
    sentence += f"{state.heading_true:.2f},T"
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gprmc(state: GNSSSimulatorState):
    sentence = "GPRMC,"
    sentence += f"{format_time_utc(state.current_time_utc)},"
    status = 'A' if state.fix_quality > 0 else 'V'
    sentence += f"{status},"
    sentence += f"{format_latitude_nmea(state.latitude)},"
    sentence += f"{format_longitude_nmea(state.longitude)},"
    sentence += f"{state.speed_knots:.2f},"
    sentence += f"{state.heading_true:.2f},"
    sentence += f"{format_date_utc(state.current_time_utc)},"
    sentence += ",," # Mag variation
    mode_indicator = 'N'
    if state.fix_quality == 1: mode_indicator = 'A' # SPS
    elif state.fix_quality == 2: mode_indicator = 'D' # DGPS/SBAS
    elif state.fix_quality == 4: mode_indicator = 'A' # RTK Fixed (often 'A' or specific like 'R')
    elif state.fix_quality == 5: mode_indicator = 'A' # RTK Float (often 'A' or specific like 'F')
    sentence += mode_indicator
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gpvtg(state: GNSSSimulatorState):
    sentence = "GPVTG,"
    sentence += f"{state.heading_true:.2f},T,"
    sentence += ",M,"
    sentence += f"{state.speed_knots:.2f},N,"
    speed_kmh = state.speed_knots * 1.852
    sentence += f"{speed_kmh:.2f},K"
    mode_indicator = 'N'
    if state.fix_quality == 1: mode_indicator = 'A'
    elif state.fix_quality == 2: mode_indicator = 'D'
    elif state.fix_quality >= 4: mode_indicator = 'A' # RTK often 'A'
    sentence += f",{mode_indicator}"
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gpgll(state: GNSSSimulatorState):
    sentence = "GPGLL,"
    sentence += f"{format_latitude_nmea(state.latitude)},"
    sentence += f"{format_longitude_nmea(state.longitude)},"
    sentence += f"{format_time_utc(state.current_time_utc)},"
    status = 'A' if state.fix_quality > 0 else 'V'
    sentence += f"{status},"
    mode_indicator = 'N'
    if state.fix_quality == 1: mode_indicator = 'A'
    elif state.fix_quality == 2: mode_indicator = 'D'
    elif state.fix_quality >= 4: mode_indicator = 'A'
    sentence += mode_indicator
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gpgsa(state: GNSSSimulatorState):
    sentence = "GPGSA,"
    sentence += f"{state.mode_gsa_1},"
    gsa_mode_2 = 1
    if state.fix_quality > 0:
        if state.num_satellites >= 4 and state.altitude_msl is not None: gsa_mode_2 = 3
        elif state.num_satellites >= 3: gsa_mode_2 = 2
        if gsa_mode_2 == 1 : gsa_mode_2 = 2 # If fix is reported, assume at least 2D
    sentence += f"{gsa_mode_2},"
    sats_to_list = state.satellite_prns[:min(len(state.satellite_prns), 12)]
    for i in range(12):
        sentence += f"{sats_to_list[i]}," if i < len(sats_to_list) else ","
    sentence += f"{state.pdop:.1f},{state.hdop:.1f},{state.vdop:.1f}"
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gpgst(state: GNSSSimulatorState):
    sentence = "GPGST,"
    sentence += f"{format_time_utc(state.current_time_utc)},"
    profile = state.gst_profile
    if state.fix_quality == 0:
        sentence += ",,,,,,,"
    else:
        sentence += f"{profile[0]:.2f},"
        sentence += f"{profile[1]:.2f},"
        sentence += f"{profile[2]:.2f},"
        sentence += f"{profile[3]:.1f},"
        sentence += f"{profile[4]:.2f},"
        sentence += f"{profile[5]:.2f},"
        sentence += f"{profile[6]:.2f}"
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

def create_gpzda(state: GNSSSimulatorState):
    sentence = "GPZDA,"
    sentence += f"{format_time_utc(state.current_time_utc)},"
    sentence += f"{state.current_time_utc.day:02d},"
    sentence += f"{state.current_time_utc.month:02d},"
    sentence += f"{state.current_time_utc.year:04d},"
    sentence += "00,00"
    return f"${sentence}*{calculate_nmea_checksum(sentence)}"

NMEA_GENERATORS = {
    "GGA": create_gpgga, "HDT": create_gphdt, "RMC": create_gprmc,
    "VTG": create_gpvtg, "GLL": create_gpgll, "GSA": create_gpgsa,
    "GST": create_gpgst, "ZDA": create_gpzda
}

# --- Network Handler Class ---
class NetworkHandler:
    def __init__(self, sim_state: GNSSSimulatorState):
        self.sim_state = sim_state
        self.udp_socket = None
        self.tcp_server_socket = None
        self.tcp_clients = []
        self.tcp_server_thread = None
        self.running = False

    def start(self):
        self.running = True
        if self.sim_state.output_mode == "UDP":
            try:
                self.udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                self.sim_state.log_message_signal.emit(f"UDP output started to {self.sim_state.udp_ip}:{self.sim_state.udp_port}")
            except Exception as e:
                self.sim_state.log_message_signal.emit(f"Error starting UDP: {e}")
                self.running = False
        elif self.sim_state.output_mode == "TCP":
            try:
                self.tcp_server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.tcp_server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.tcp_server_socket.bind((self.sim_state.tcp_ip, self.sim_state.tcp_port))
                self.tcp_server_socket.listen(5)
                self.sim_state.log_message_signal.emit(f"TCP server listening on {self.sim_state.tcp_ip}:{self.sim_state.tcp_port}")
                self.tcp_server_thread = threading.Thread(target=self._accept_tcp_clients, daemon=True)
                self.tcp_server_thread.start()
            except Exception as e:
                self.sim_state.log_message_signal.emit(f"Error starting TCP server: {e}")
                self.running = False
        return self.running

    def _accept_tcp_clients(self):
        while self.running and self.tcp_server_socket:
            try:
                client_socket, addr = self.tcp_server_socket.accept()
                self.sim_state.log_message_signal.emit(f"TCP client connected: {addr}")
                self.tcp_clients.append(client_socket)
            except socket.error as e:
                if self.running: # Only log if we weren't expecting the stop
                     self.sim_state.log_message_signal.emit(f"TCP accept error: {e}")
                break # Exit loop if socket error (e.g. closed)
            except Exception as e:
                if self.running:
                    self.sim_state.log_message_signal.emit(f"Unexpected TCP accept error: {e}")
                break


    def send(self, message_str: str):
        if not self.running:
            return
        message_bytes = (message_str + "\r\n").encode('ascii')

        if self.sim_state.output_mode == "UDP" and self.udp_socket:
            try:
                self.udp_socket.sendto(message_bytes, (self.sim_state.udp_ip, self.sim_state.udp_port))
            except Exception as e:
                self.sim_state.log_message_signal.emit(f"UDP send error: {e}")
        elif self.sim_state.output_mode == "TCP":
            clients_to_remove = []
            for client in self.tcp_clients:
                try:
                    client.sendall(message_bytes)
                except socket.error:
                    self.sim_state.log_message_signal.emit(f"TCP client disconnected or error. Removing.")
                    clients_to_remove.append(client)
                except Exception as e:
                    self.sim_state.log_message_signal.emit(f"TCP send error: {e}")
                    clients_to_remove.append(client) # Also remove on other errors
            for client in clients_to_remove:
                if client in self.tcp_clients:
                    self.tcp_clients.remove(client)
                    try:
                        client.close()
                    except: pass


    def stop(self):
        self.running = False
        if self.udp_socket:
            self.udp_socket.close()
            self.udp_socket = None
            self.sim_state.log_message_signal.emit("UDP output stopped.")
        if self.tcp_server_socket:
            # Shut down the server socket to unblock accept
            try:
                self.tcp_server_socket.shutdown(socket.SHUT_RDWR)
            except OSError: pass # May already be closed or not connected
            except Exception: pass


            self.tcp_server_socket.close()
            self.tcp_server_socket = None
            if self.tcp_server_thread and self.tcp_server_thread.is_alive():
                self.tcp_server_thread.join(timeout=1.0) # Wait for thread to finish

            for client in self.tcp_clients:
                try:
                    client.close()
                except: pass
            self.tcp_clients.clear()
            self.sim_state.log_message_signal.emit("TCP server stopped.")


# --- PyQt6 GUI Application Class ---
class GNSSSimApp(QMainWindow):
    def __init__(self):
        super().__init__()
        # Core components
        self.sim_state = GNSSSimulatorState()
        self.path_controller = PathController(self.sim_state)
        self.network_handler = NetworkHandler(self.sim_state)

        # Connect sim_state's log signal to the GUI's log method
        self.sim_state.log_message_signal = self.log_message # type: ignore

        self.initUI()
        self.setup_timer()
        self.load_initial_settings()

    def initUI(self):
        self.setWindowTitle("GNSS Simulator")
        self.setGeometry(100, 100, 900, 700)

        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)

        # --- Left Panel: Controls ---
        left_panel = QVBoxLayout()

        # Output Configuration
        output_group = QGroupBox("Output Configuration")
        output_layout = QGridLayout()
        self.udp_radio = QRadioButton("UDP")
        self.udp_radio.setChecked(True)
        self.tcp_radio = QRadioButton("TCP")
        output_layout.addWidget(self.udp_radio, 0, 0)
        output_layout.addWidget(self.tcp_radio, 0, 1)
        output_layout.addWidget(QLabel("IP Address:"), 1, 0)
        self.ip_edit = QLineEdit(self.sim_state.udp_ip)
        output_layout.addWidget(self.ip_edit, 1, 1)
        output_layout.addWidget(QLabel("Port:"), 2, 0)
        self.port_edit = QLineEdit(str(self.sim_state.udp_port))
        output_layout.addWidget(self.port_edit, 2, 1)
        output_layout.addWidget(QLabel("Update Rate (Hz):"), 3, 0)
        self.rate_edit = QLineEdit(str(self.sim_state.update_rate_hz))
        output_layout.addWidget(self.rate_edit, 3, 1)
        self.start_button = QPushButton("Start Output")
        self.start_button.clicked.connect(self.toggle_simulation)
        output_layout.addWidget(self.start_button, 4, 0, 1, 2)
        output_group.setLayout(output_layout)
        left_panel.addWidget(output_group)

        # NMEA Sentences
        nmea_group = QGroupBox("NMEA Sentences to Output")
        nmea_layout = QGridLayout()
        self.nmea_checkboxes = {}
        row, col = 0, 0
        for name in NMEA_GENERATORS.keys():
            cb = QCheckBox(name)
            cb.setChecked(True) # Default to all on
            self.nmea_checkboxes[name] = cb
            nmea_layout.addWidget(cb, row, col)
            col += 1
            if col > 2: col = 0; row += 1
        nmea_group.setLayout(nmea_layout)
        left_panel.addWidget(nmea_group)

        # GNSS Parameters
        gnss_params_group = QGroupBox("GNSS Parameters")
        gnss_params_layout = QGridLayout()
        gnss_params_layout.addWidget(QLabel("Fix Quality:"), 0, 0)
        self.fix_quality_combo = QComboBox()
        self.fix_quality_map = {
            "0 - Invalid": 0, "1 - SPS": 1, "2 - DGPS/SBAS": 2,
            "4 - RTK Fixed": 4, "5 - RTK Float": 5
        }
        self.fix_quality_combo.addItems(self.fix_quality_map.keys())
        self.fix_quality_combo.setCurrentIndex(1) # Default to SPS
        gnss_params_layout.addWidget(self.fix_quality_combo, 0, 1)
        gnss_params_layout.addWidget(QLabel("Num Satellites:"), 1, 0)
        self.num_sats_edit = QLineEdit(str(self.sim_state.num_satellites))
        gnss_params_layout.addWidget(self.num_sats_edit, 1, 1)
        gnss_params_layout.addWidget(QLabel("HDOP:"), 2, 0)
        self.hdop_edit = QLineEdit(str(self.sim_state.hdop))
        gnss_params_layout.addWidget(self.hdop_edit, 2, 1)
        # Add VDOP, PDOP if needed
        gnss_params_group.setLayout(gnss_params_layout)
        left_panel.addWidget(gnss_params_group)

        # Simulation Mode
        sim_mode_group = QGroupBox("Simulation Mode")
        sim_mode_layout = QVBoxLayout()
        self.manual_mode_radio = QRadioButton("Manual Control")
        self.manual_mode_radio.setChecked(True)
        self.preset_mode_radio = QRadioButton("Preset Path")
        sim_mode_layout.addWidget(self.manual_mode_radio)
        sim_mode_layout.addWidget(self.preset_mode_radio)

        # Manual Controls (initially more visible)
        self.manual_controls_group = QGroupBox("Manual Controls")
        manual_layout = QGridLayout()
        manual_layout.addWidget(QLabel("Start Lat:"), 0, 0)
        self.manual_lat_edit = QLineEdit(str(self.sim_state.latitude))
        manual_layout.addWidget(self.manual_lat_edit, 0, 1)
        manual_layout.addWidget(QLabel("Start Lon:"), 1, 0)
        self.manual_lon_edit = QLineEdit(str(self.sim_state.longitude))
        manual_layout.addWidget(self.manual_lon_edit, 1, 1)
        manual_layout.addWidget(QLabel("Start Alt (m):"), 2, 0)
        self.manual_alt_edit = QLineEdit(str(self.sim_state.altitude_msl))
        manual_layout.addWidget(self.manual_alt_edit, 2, 1)
        manual_layout.addWidget(QLabel("Start Hdg (deg):"), 3, 0)
        self.manual_hdg_edit = QLineEdit(str(self.sim_state.heading_true))
        manual_layout.addWidget(self.manual_hdg_edit, 3, 1)
        self.set_start_pos_button = QPushButton("Set Start Position")
        self.set_start_pos_button.clicked.connect(self.apply_manual_start_position)
        manual_layout.addWidget(self.set_start_pos_button, 4, 0, 1, 2)
        manual_layout.addWidget(QLabel("Target Speed (kts):"), 5, 0)
        self.manual_speed_edit = QLineEdit(str(self.sim_state.manual_target_speed_knots))
        manual_layout.addWidget(self.manual_speed_edit, 5, 1)
        manual_layout.addWidget(QLabel("Target Hdg (deg):"), 6, 0)
        self.manual_target_hdg_edit = QLineEdit(str(self.sim_state.manual_target_heading_deg))
        manual_layout.addWidget(self.manual_target_hdg_edit, 6, 1)
        manual_layout.addWidget(QLabel("Rate of Turn (dps):"), 7, 0)
        self.manual_rot_edit = QLineEdit(str(self.sim_state.manual_rate_of_turn_dps))
        manual_layout.addWidget(self.manual_rot_edit, 7, 1)
        self.manual_controls_group.setLayout(manual_layout)
        sim_mode_layout.addWidget(self.manual_controls_group)

        # Preset Path Controls (initially less visible or disabled)
        self.preset_controls_group = QGroupBox("Preset Path Controls")
        preset_layout = QGridLayout()
        preset_layout.addWidget(QLabel("Select Path:"), 0, 0)
        self.path_combo = QComboBox()
        self.path_combo.addItems(self.path_controller.preset_paths.keys())
        preset_layout.addWidget(self.path_combo, 0, 1)
        self.load_path_button = QPushButton("Load Path")
        self.load_path_button.clicked.connect(self.load_selected_path)
        preset_layout.addWidget(self.load_path_button, 1, 0, 1, 2)
        self.preset_controls_group.setLayout(preset_layout)
        self.preset_controls_group.setEnabled(False) # Disabled by default
        sim_mode_layout.addWidget(self.preset_controls_group)

        sim_mode_group.setLayout(sim_mode_layout)
        left_panel.addWidget(sim_mode_group)
        left_panel.addStretch(1)

        # --- Right Panel: Status and Log ---
        right_panel = QVBoxLayout()

        status_group = QGroupBox("Current Status")
        status_layout = QGridLayout()
        self.status_labels = {
            "Latitude:": QLabel("N/A"), "Longitude:": QLabel("N/A"),
            "Altitude (m):": QLabel("N/A"), "Speed (kts):": QLabel("N/A"),
            "Heading (deg):": QLabel("N/A"), "Fix Quality:": QLabel("N/A"),
            "Sats:": QLabel("N/A"), "Mode:": QLabel("N/A"),
            "Path:": QLabel("N/A"), "Waypoint Idx:": QLabel("N/A")
        }
        row = 0
        for text, label in self.status_labels.items():
            status_layout.addWidget(QLabel(text), row, 0)
            status_layout.addWidget(label, row, 1)
            row += 1
        status_group.setLayout(status_layout)
        right_panel.addWidget(status_group)

        log_group = QGroupBox("Log / Last Messages")
        log_layout = QVBoxLayout()
        self.log_display = QPlainTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setMaximumBlockCount(200) # Keep log size manageable
        log_layout.addWidget(self.log_display)
        log_group.setLayout(log_layout)
        right_panel.addWidget(log_group)

        main_layout.addLayout(left_panel, 1) # Proportion 1
        main_layout.addLayout(right_panel, 1) # Proportion 1

        # Connect mode radio buttons
        self.manual_mode_radio.toggled.connect(self.update_simulation_mode_controls)
        self.preset_mode_radio.toggled.connect(self.update_simulation_mode_controls)


    def setup_timer(self):
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.simulation_step)

    def load_initial_settings(self):
        # This could load from a config file in the future
        self.update_sim_state_from_gui() # Ensure sim_state reflects initial GUI values

    def update_sim_state_from_gui(self):
        # Output config
        self.sim_state.output_mode = "TCP" if self.tcp_radio.isChecked() else "UDP"
        self.sim_state.udp_ip = self.ip_edit.text()
        self.sim_state.tcp_ip = self.ip_edit.text() # Assuming same IP for TCP listen for now
        try:
            self.sim_state.udp_port = int(self.port_edit.text())
            self.sim_state.tcp_port = int(self.port_edit.text()) # Assuming same port
        except ValueError:
            self.log_message("Invalid port number. Using default.")
            self.port_edit.setText(str(10110))
            self.sim_state.udp_port = 10110
            self.sim_state.tcp_port = 10110
        try:
            self.sim_state.update_rate_hz = float(self.rate_edit.text())
            if self.sim_state.update_rate_hz <= 0: self.sim_state.update_rate_hz = 1.0
        except ValueError:
            self.log_message("Invalid update rate. Using 1.0 Hz.")
            self.rate_edit.setText("1.0")
            self.sim_state.update_rate_hz = 1.0

        # GNSS Params
        selected_fix_text = self.fix_quality_combo.currentText()
        self.sim_state.fix_quality = self.fix_quality_map.get(selected_fix_text, 1)
        try:
            self.sim_state.num_satellites = int(self.num_sats_edit.text())
        except ValueError: self.sim_state.num_satellites = 8
        try:
            self.sim_state.hdop = float(self.hdop_edit.text())
        except ValueError: self.sim_state.hdop = 1.0
        # Update VDOP, PDOP similarly if added

        # Sim Mode
        self.sim_state.simulation_mode = "preset_path" if self.preset_mode_radio.isChecked() else "manual"

        # Manual Controls (read them into sim_state's manual targets)
        try:
            self.sim_state.manual_target_speed_knots = float(self.manual_speed_edit.text())
            self.sim_state.manual_target_heading_deg = float(self.manual_target_hdg_edit.text())
            self.sim_state.manual_rate_of_turn_dps = float(self.manual_rot_edit.text())
        except ValueError:
            self.log_message("Invalid manual speed/heading/RoT. Using 0.")
            self.sim_state.manual_target_speed_knots = 0.0
            self.sim_state.manual_target_heading_deg = 0.0
            self.sim_state.manual_rate_of_turn_dps = 0.0
        # Start position is applied by its own button

    def apply_manual_start_position(self):
        try:
            lat = float(self.manual_lat_edit.text())
            lon = float(self.manual_lon_edit.text())
            alt = float(self.manual_alt_edit.text())
            hdg = float(self.manual_hdg_edit.text())
            self.sim_state.set_start_position(lat, lon, alt, hdg)
            self.update_gui_displays() # Reflect immediately
        except ValueError:
            self.log_message("Invalid start position values.")
            QMessageBox.warning(self, "Input Error", "Invalid numeric value for start position.")


    def update_simulation_mode_controls(self):
        is_manual = self.manual_mode_radio.isChecked()
        self.manual_controls_group.setEnabled(is_manual)
        self.preset_controls_group.setEnabled(not is_manual)
        self.sim_state.simulation_mode = "manual" if is_manual else "preset_path"
        if not is_manual and not self.sim_state.on_path: # If switching to preset, and no path loaded
            self.log_message("Switched to Preset Path mode. Select and load a path.")
        elif is_manual:
            self.sim_state.on_path = False # Ensure path following is off if switching to manual


    def load_selected_path(self):
        if self.preset_mode_radio.isChecked():
            path_name = self.path_combo.currentText()
            self.path_controller.load_path(path_name)
            self.update_gui_displays() # Reflect new path state

    def toggle_simulation(self):
        if self.sim_state.simulation_running:
            self.stop_simulation()
        else:
            self.start_simulation()

    def start_simulation(self):
        self.update_sim_state_from_gui() # Ensure all current settings are captured

        if not self.network_handler.start(): # Try to start network output
            QMessageBox.critical(self, "Network Error", "Failed to start network output. Check IP/Port and logs.")
            return

        self.sim_state.simulation_running = True
        self.start_button.setText("Stop Output")
        self.log_message(f"Simulation started. Output: {self.sim_state.output_mode}, Rate: {self.sim_state.update_rate_hz} Hz")
        self.sim_state.last_update_sim_time = time.monotonic() # Reset delta t calculation
        
        # Set timer interval based on update rate
        timer_interval_ms = int(1000 / self.sim_state.update_rate_hz)
        if timer_interval_ms <=0: timer_interval_ms = 1000 # Default to 1s if rate is invalid
        self.timer.start(timer_interval_ms)
        
        # Disable config controls while running
        self._set_config_controls_enabled(False)


    def stop_simulation(self):
        self.sim_state.simulation_running = False
        self.timer.stop()
        self.network_handler.stop()
        self.start_button.setText("Start Output")
        self.log_message("Simulation stopped.")
        # Re-enable config controls
        self._set_config_controls_enabled(True)

    def _set_config_controls_enabled(self, enabled: bool):
        # Output group
        self.udp_radio.setEnabled(enabled)
        self.tcp_radio.setEnabled(enabled)
        self.ip_edit.setEnabled(enabled)
        self.port_edit.setEnabled(enabled)
        self.rate_edit.setEnabled(enabled)
        # Sim mode selection
        self.manual_mode_radio.setEnabled(enabled)
        self.preset_mode_radio.setEnabled(enabled)
        # Path loading (only if preset mode is selected AND enabled)
        self.load_path_button.setEnabled(enabled and self.preset_mode_radio.isChecked())
        self.path_combo.setEnabled(enabled and self.preset_mode_radio.isChecked())
        # Manual start position button
        self.set_start_pos_button.setEnabled(enabled and self.manual_mode_radio.isChecked())


    def simulation_step(self):
        if not self.sim_state.simulation_running:
            return

        # 1. Update simulation state from GUI (for parameters that can change live)
        self.update_sim_state_from_gui() # This captures things like fix quality, manual speed/hdg targets

        # 2. Core simulation logic
        self.sim_state.update_simulation() # Updates internal state, position based on manual/path targets
        if self.sim_state.simulation_mode == "preset_path" and self.sim_state.on_path:
            update_interval = 1.0 / self.sim_state.update_rate_hz if self.sim_state.update_rate_hz > 0 else 1.0
            self.path_controller.update(update_interval)

        # 3. Generate NMEA sentences
        nmea_messages_to_send = []
        for name, checkbox in self.nmea_checkboxes.items():
            if checkbox.isChecked():
                generator_func = NMEA_GENERATORS.get(name)
                if generator_func:
                    nmea_messages_to_send.append(generator_func(self.sim_state))

        # 4. Send NMEA data
        for msg in nmea_messages_to_send:
            self.network_handler.send(msg)
            # self.log_message(f"Sent: {msg}") # Logging every sent message can be too verbose

        # 5. Update GUI display
        self.update_gui_displays()

    def update_gui_displays(self):
        s = self.sim_state
        self.status_labels["Latitude:"].setText(f"{s.latitude:.6f}")
        self.status_labels["Longitude:"].setText(f"{s.longitude:.6f}")
        self.status_labels["Altitude (m):"].setText(f"{s.altitude_msl:.2f}")
        self.status_labels["Speed (kts):"].setText(f"{s.speed_knots:.2f}")
        self.status_labels["Heading (deg):"].setText(f"{s.heading_true:.2f}")

        fix_text = [k for k, v in self.fix_quality_map.items() if v == s.fix_quality]
        self.status_labels["Fix Quality:"].setText(fix_text[0] if fix_text else "Unknown")
        self.status_labels["Sats:"].setText(str(s.num_satellites))
        self.status_labels["Mode:"].setText(s.simulation_mode.capitalize())

        if s.simulation_mode == "preset_path" and s.on_path:
            self.status_labels["Path:"].setText(self.path_controller.current_path_name or "N/A")
            self.status_labels["Waypoint Idx:"].setText(str(self.path_controller.current_waypoint_index))
        else:
            self.status_labels["Path:"].setText("N/A (Manual Mode)")
            self.status_labels["Waypoint Idx:"].setText("N/A")


    def log_message(self, message):
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self.log_display.appendPlainText(f"[{timestamp}] {message}")

    def closeEvent(self, event):
        # Cleanly stop simulation and network threads/sockets on window close
        self.stop_simulation()
        super().closeEvent(event)


# --- Main Execution ---
if __name__ == '__main__':
    # Ensure pyqtSignal is available if running directly
    # This is a bit of a hack for the conditional import in GNSSSimulatorState
    # In a real app, you'd ensure PyQt6 is properly installed.
    if 'pyqtSignal' not in globals():
        from PyQt6.QtCore import pyqtSignal
        GNSSSimulatorState.log_message_signal = pyqtSignal(str)


    QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_ShareOpenGLContexts) # Suggested for some systems
    app = QApplication(sys.argv)
    ex = GNSSSimApp()
    ex.show()
    sys.exit(app.exec())
```

**To Run This Code:**

1.  **Ensure PyQt6 is installed:**
    ```bash
    pip install PyQt6
    ```
2.  Save the code above as a Python file (e.g., `gnss_simulator_app.py`).
3.  Run it from your terminal: `python gnss_simulator_app.py`.

**Key Features Implemented:**

* **GUI:**
    * Controls for output (UDP/TCP, IP, Port, Rate).
    * Checkboxes for selecting NMEA sentences.
    * Inputs for GNSS parameters (Fix Quality, Sats, HDOP).
    * Selection between "Manual Control" and "Preset Path" modes.
    * Manual controls for start position, target speed, target heading, and rate of turn.
    * Preset path selection and loading.
    * Status display for current position, speed, heading, fix, etc.
    * Log display for messages and errors.
* **Networking:**
    * UDP output to a specified IP and port.
    * TCP server that listens for client connections and sends data to all connected clients.
    * TCP client handling (accepting new connections, removing disconnected ones) is done in a separate thread.
* **Simulation Core:**
    * The `GNSSSimulatorState` and `PathController` manage the underlying simulation logic.
    * A `QTimer` drives the simulation loop at the specified update rate.
* **Integration:**
    * GUI controls update the `sim_state` and trigger actions.
    * The simulation loop updates the `sim_state`, generates NMEA data, sends it via the `NetworkHandler`, and updates the GUI status displays.

**How to Use:**

1.  Configure the "Output Configuration" (IP, Port, Rate, UDP/TCP). For TCP, the IP is the address the server will listen on (e.g., `0.0.0.0` for all interfaces, or `127.0.0.1` for local only).
2.  Select the NMEA sentences you want to output.
3.  Set GNSS parameters like Fix Quality.
4.  Choose a "Simulation Mode":
    * **Manual Control:**
        * Optionally, use "Set Start Position" to define the vessel's initial state.
        * Set "Target Speed," "Target Hdg," or "Rate of Turn." The simulator will try to achieve these.
    * **Preset Path:**
        * Select a path from the dropdown.
        * Click "Load Path." The vessel will start at the path's defined origin.
5.  Click "Start Output."
6.  To receive data:
    * **UDP:** Use a UDP listener (like netcat: `nc -ul -p <PORT>`) on the target machine.
    * **TCP:** Connect a TCP client (like netcat: `nc <SERVER_IP> <PORT>`) to the simulator.
7.  Observe the GUI status and log for updates.
8.  Click "Stop Output" to halt the simulation and network transmission.

This is a complex application, and while I've tested the structure and flow, thorough testing in various scenarios would be needed. The error handling is basic; more robust error reporting could be added. The TCP server accept loop is daemonized, which is generally okay for this type of application, but more graceful shutdown logic could be implemented if need