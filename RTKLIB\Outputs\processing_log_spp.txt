2025-06-02 21:01:09,923 [INFO] - Logging to console and to file: C:\Dev\RTKLIB\Outputs\processing_log_spp.txt (Level: INFO)
2025-06-02 21:01:09,923 [INFO] - Output directory set to: C:\Dev\RTKLIB\Outputs
2025-06-02 21:02:15,831 [WARNING] - File not found or is not a file: "C:\Dev\RTKLIB\conf.ini"
2025-06-02 21:02:24,246 [WARNING] - Ensure your .conf file is correctly set up for SPP or PPP (e.g., pos1-posmode = single).
2025-06-02 21:02:24,246 [WARNING] - A template .conf for SPP is provided in the script documentation.
2025-06-02 21:02:24,246 [INFO] - --- Path Configuration Complete ---
2025-06-02 21:02:24,248 [INFO] - --- Checking Prerequisites ---
2025-06-02 21:02:24,248 [INFO] - Found RNX2RTKP Executable: C:\Dev\RTKLIB\RTKLIB_2.4.3_b34\RTKLIB_2.4.3_b34\bin\rnx2rtkp.exe
2025-06-02 21:02:24,248 [INFO] - Found POS2KML Executable: C:\Dev\RTKLIB\RTKLIB_2.4.3_b34\RTKLIB_2.4.3_b34\bin\pos2kml.exe
2025-06-02 21:02:24,248 [INFO] - Found Main Rover RINEX Directory: C:\Dev\RTKLIB\Inputs
2025-06-02 21:02:24,248 [INFO] - Found Navigation Files Directory: C:\Dev\RTKLIB\Inputs
2025-06-02 21:02:24,248 [INFO] - Found RTKLIB Configuration File: C:\Dev\RTKLIB\conf.ini
2025-06-02 21:02:24,248 [INFO] - Found Output Directory: C:\Dev\RTKLIB\Outputs
2025-06-02 21:02:24,248 [INFO] - --- Starting RTKLIB Standalone Batch Processing ---
2025-06-02 21:02:25,709 [INFO] - Found 48 file(s) in 'C:\Dev\RTKLIB\Inputs' matching criteria.
2025-06-02 21:02:27,149 [INFO] - Found 48 file(s) in 'C:\Dev\RTKLIB\Inputs' matching criteria.
2025-06-02 21:02:28,582 [INFO] - Found 48 file(s) in 'C:\Dev\RTKLIB\Inputs' matching criteria.
2025-06-02 21:02:30,008 [INFO] - Found 48 file(s) in 'C:\Dev\RTKLIB\Inputs' matching criteria.
2025-06-02 21:02:31,456 [WARNING] - No files found in 'C:\Dev\RTKLIB\Inputs' matching criteria: ['.00c', '.01c', '.02c', '.03c', '.04c', '.05c', '.06c', '.07c', '.08c', '.09c', '.10c', '.11c', '.12c', '.13c', '.14c', '.15c', '.16c', '.17c', '.18c', '.19c', '.20c', '.21c', '.22c', '.23c', '.24c', '.25c', '.26c', '.27c', '.28c', '.29c', '.30c', '.31c', '.32c', '.33c', '.34c', '.35c', '.36c', '.37c', '.38c', '.39c', '.40c', '.41c', '.42c', '.43c', '.44c', '.45c', '.46c', '.47c', '.48c', '.49c', '.50c', '.51c', '.52c', '.53c', '.54c', '.55c', '.56c', '.57c', '.58c', '.59c', '.60c', '.61c', '.62c', '.63c', '.64c', '.65c', '.66c', '.67c', '.68c', '.69c', '.70c', '.71c', '.72c', '.73c', '.74c', '.75c', '.76c', '.77c', '.78c', '.79c', '.80c', '.81c', '.82c', '.83c', '.84c', '.85c', '.86c', '.87c', '.88c', '.89c', '.90c', '.91c', '.92c', '.93c', '.94c', '.95c', '.96c', '.97c', '.98c', '.99c', '.eph', '.nav']
2025-06-02 21:02:32,888 [INFO] - Found 48 file(s) in 'C:\Dev\RTKLIB\Inputs' matching criteria.
2025-06-02 21:02:34,321 [WARNING] - No files found in 'C:\Dev\RTKLIB\Inputs' matching criteria: ['.00p', '.01p', '.02p', '.03p', '.04p', '.05p', '.06p', '.07p', '.08p', '.09p', '.10p', '.11p', '.12p', '.13p', '.14p', '.15p', '.16p', '.17p', '.18p', '.19p', '.20p', '.21p', '.22p', '.23p', '.24p', '.25p', '.26p', '.27p', '.28p', '.29p', '.30p', '.31p', '.32p', '.33p', '.34p', '.35p', '.36p', '.37p', '.38p', '.39p', '.40p', '.41p', '.42p', '.43p', '.44p', '.45p', '.46p', '.47p', '.48p', '.49p', '.50p', '.51p', '.52p', '.53p', '.54p', '.55p', '.56p', '.57p', '.58p', '.59p', '.60p', '.61p', '.62p', '.63p', '.64p', '.65p', '.66p', '.67p', '.68p', '.69p', '.70p', '.71p', '.72p', '.73p', '.74p', '.75p', '.76p', '.77p', '.78p', '.79p', '.80p', '.81p', '.82p', '.83p', '.84p', '.85p', '.86p', '.87p', '.88p', '.89p', '.90p', '.91p', '.92p', '.93p', '.94p', '.95p', '.96p', '.97p', '.98p', '.99p', '.eph', '.nav']
2025-06-02 21:02:34,321 [INFO] - Found 192 navigation files to use:
2025-06-02 21:02:34,321 [INFO] -   - LD900_25APR17_002304.25G
2025-06-02 21:02:34,321 [INFO] -   - LD900_25APR17_002304.25L
2025-06-02 21:02:34,321 [INFO] -   - LD900_25APR17_002304.25N
2025-06-02 21:02:34,322 [INFO] -   - LD900_25APR17_002304.25Q
2025-06-02 21:02:34,322 [INFO] -   - LD900_25APR17_005304.25G
2025-06-02 21:02:34,322 [INFO] -   - ... and 187 more.
2025-06-02 21:02:34,322 [INFO] - Storing individual POS/STAT files in: individual_pos_files_spp
2025-06-02 21:02:34,323 [INFO] - 
--- Processing 48 Rover Observation Files ---
2025-06-02 21:02:34,323 [INFO] - --- File 1/48: LD900_25APR17_002304.25O (from .\LD900_25APR17_002304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:34,323 [INFO] - Processing: LD900_25APR17_002304.25O (from .\LD900_25APR17_002304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:34,323 [INFO] -   Unique Output Stem: LD900_25APR17_002304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_002304
2025-06-02 21:02:34,323 [INFO] -   Output POS: LD900_25APR17_002304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_002304.pos, STAT: LD900_25APR17_002304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_002304.stat
2025-06-02 21:02:36,298 [INFO] - Successfully processed LD900_25APR17_002304.25O.
2025-06-02 21:02:36,298 [INFO] - --- File 2/48: LD900_25APR17_005304.25O (from .\LD900_25APR17_005304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:36,299 [INFO] - Processing: LD900_25APR17_005304.25O (from .\LD900_25APR17_005304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:36,299 [INFO] -   Unique Output Stem: LD900_25APR17_005304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_005304
2025-06-02 21:02:36,299 [INFO] -   Output POS: LD900_25APR17_005304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_005304.pos, STAT: LD900_25APR17_005304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_005304.stat
2025-06-02 21:02:38,323 [INFO] - Successfully processed LD900_25APR17_005304.25O.
2025-06-02 21:02:38,323 [INFO] - --- File 3/48: LD900_25APR17_012304.25O (from .\LD900_25APR17_012304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:38,323 [INFO] - Processing: LD900_25APR17_012304.25O (from .\LD900_25APR17_012304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:38,323 [INFO] -   Unique Output Stem: LD900_25APR17_012304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_012304
2025-06-02 21:02:38,323 [INFO] -   Output POS: LD900_25APR17_012304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_012304.pos, STAT: LD900_25APR17_012304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_012304.stat
2025-06-02 21:02:40,336 [INFO] - Successfully processed LD900_25APR17_012304.25O.
2025-06-02 21:02:40,337 [INFO] - --- File 4/48: LD900_25APR17_015304.25O (from .\LD900_25APR17_015304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:40,337 [INFO] - Processing: LD900_25APR17_015304.25O (from .\LD900_25APR17_015304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:40,337 [INFO] -   Unique Output Stem: LD900_25APR17_015304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_015304
2025-06-02 21:02:40,337 [INFO] -   Output POS: LD900_25APR17_015304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_015304.pos, STAT: LD900_25APR17_015304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_015304.stat
2025-06-02 21:02:42,572 [INFO] - Successfully processed LD900_25APR17_015304.25O.
2025-06-02 21:02:42,572 [INFO] - --- File 5/48: LD900_25APR17_022304.25O (from .\LD900_25APR17_022304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:42,572 [INFO] - Processing: LD900_25APR17_022304.25O (from .\LD900_25APR17_022304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:42,572 [INFO] -   Unique Output Stem: LD900_25APR17_022304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_022304
2025-06-02 21:02:42,572 [INFO] -   Output POS: LD900_25APR17_022304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_022304.pos, STAT: LD900_25APR17_022304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_022304.stat
2025-06-02 21:02:44,711 [INFO] - Successfully processed LD900_25APR17_022304.25O.
2025-06-02 21:02:44,711 [INFO] - --- File 6/48: LD900_25APR17_025304.25O (from .\LD900_25APR17_025304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:44,711 [INFO] - Processing: LD900_25APR17_025304.25O (from .\LD900_25APR17_025304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:44,711 [INFO] -   Unique Output Stem: LD900_25APR17_025304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_025304
2025-06-02 21:02:44,711 [INFO] -   Output POS: LD900_25APR17_025304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_025304.pos, STAT: LD900_25APR17_025304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_025304.stat
2025-06-02 21:02:46,991 [INFO] - Successfully processed LD900_25APR17_025304.25O.
2025-06-02 21:02:46,991 [INFO] - --- File 7/48: LD900_25APR17_032304.25O (from .\LD900_25APR17_032304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:46,991 [INFO] - Processing: LD900_25APR17_032304.25O (from .\LD900_25APR17_032304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:46,991 [INFO] -   Unique Output Stem: LD900_25APR17_032304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_032304
2025-06-02 21:02:46,991 [INFO] -   Output POS: LD900_25APR17_032304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_032304.pos, STAT: LD900_25APR17_032304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_032304.stat
2025-06-02 21:02:49,322 [INFO] - Successfully processed LD900_25APR17_032304.25O.
2025-06-02 21:02:49,323 [INFO] - --- File 8/48: LD900_25APR17_035304.25O (from .\LD900_25APR17_035304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:49,323 [INFO] - Processing: LD900_25APR17_035304.25O (from .\LD900_25APR17_035304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:49,323 [INFO] -   Unique Output Stem: LD900_25APR17_035304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_035304
2025-06-02 21:02:49,323 [INFO] -   Output POS: LD900_25APR17_035304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_035304.pos, STAT: LD900_25APR17_035304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_035304.stat
2025-06-02 21:02:51,780 [INFO] - Successfully processed LD900_25APR17_035304.25O.
2025-06-02 21:02:51,780 [INFO] - --- File 9/48: LD900_25APR17_042304.25O (from .\LD900_25APR17_042304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:51,780 [INFO] - Processing: LD900_25APR17_042304.25O (from .\LD900_25APR17_042304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:51,780 [INFO] -   Unique Output Stem: LD900_25APR17_042304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_042304
2025-06-02 21:02:51,780 [INFO] -   Output POS: LD900_25APR17_042304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_042304.pos, STAT: LD900_25APR17_042304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_042304.stat
2025-06-02 21:02:54,176 [INFO] - Successfully processed LD900_25APR17_042304.25O.
2025-06-02 21:02:54,176 [INFO] - --- File 10/48: LD900_25APR17_045304.25O (from .\LD900_25APR17_045304\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:54,176 [INFO] - Processing: LD900_25APR17_045304.25O (from .\LD900_25APR17_045304\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:54,176 [INFO] -   Unique Output Stem: LD900_25APR17_045304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_045304
2025-06-02 21:02:54,176 [INFO] -   Output POS: LD900_25APR17_045304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_045304.pos, STAT: LD900_25APR17_045304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_045304.stat
2025-06-02 21:02:56,449 [INFO] - Successfully processed LD900_25APR17_045304.25O.
2025-06-02 21:02:56,449 [INFO] - --- File 11/48: LD900_25APR17_052305.25O (from .\LD900_25APR17_052305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:56,449 [INFO] - Processing: LD900_25APR17_052305.25O (from .\LD900_25APR17_052305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:56,449 [INFO] -   Unique Output Stem: LD900_25APR17_052305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_052305
2025-06-02 21:02:56,450 [INFO] -   Output POS: LD900_25APR17_052305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_052305.pos, STAT: LD900_25APR17_052305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_052305.stat
2025-06-02 21:02:58,774 [INFO] - Successfully processed LD900_25APR17_052305.25O.
2025-06-02 21:02:58,774 [INFO] - --- File 12/48: LD900_25APR17_055305.25O (from .\LD900_25APR17_055305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:02:58,774 [INFO] - Processing: LD900_25APR17_055305.25O (from .\LD900_25APR17_055305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:02:58,774 [INFO] -   Unique Output Stem: LD900_25APR17_055305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_055305
2025-06-02 21:02:58,774 [INFO] -   Output POS: LD900_25APR17_055305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_055305.pos, STAT: LD900_25APR17_055305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_055305.stat
2025-06-02 21:03:00,793 [INFO] - Successfully processed LD900_25APR17_055305.25O.
2025-06-02 21:03:00,793 [INFO] - --- File 13/48: LD900_25APR17_062305.25O (from .\LD900_25APR17_062305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:00,793 [INFO] - Processing: LD900_25APR17_062305.25O (from .\LD900_25APR17_062305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:00,793 [INFO] -   Unique Output Stem: LD900_25APR17_062305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_062305
2025-06-02 21:03:00,793 [INFO] -   Output POS: LD900_25APR17_062305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_062305.pos, STAT: LD900_25APR17_062305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_062305.stat
2025-06-02 21:03:02,702 [INFO] - Successfully processed LD900_25APR17_062305.25O.
2025-06-02 21:03:02,703 [INFO] - --- File 14/48: LD900_25APR17_065305.25O (from .\LD900_25APR17_065305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:02,703 [INFO] - Processing: LD900_25APR17_065305.25O (from .\LD900_25APR17_065305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:02,703 [INFO] -   Unique Output Stem: LD900_25APR17_065305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_065305
2025-06-02 21:03:02,703 [INFO] -   Output POS: LD900_25APR17_065305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_065305.pos, STAT: LD900_25APR17_065305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_065305.stat
2025-06-02 21:03:04,680 [INFO] - Successfully processed LD900_25APR17_065305.25O.
2025-06-02 21:03:04,680 [INFO] - --- File 15/48: LD900_25APR17_072305.25O (from .\LD900_25APR17_072305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:04,680 [INFO] - Processing: LD900_25APR17_072305.25O (from .\LD900_25APR17_072305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:04,680 [INFO] -   Unique Output Stem: LD900_25APR17_072305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_072305
2025-06-02 21:03:04,680 [INFO] -   Output POS: LD900_25APR17_072305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_072305.pos, STAT: LD900_25APR17_072305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_072305.stat
2025-06-02 21:03:06,724 [INFO] - Successfully processed LD900_25APR17_072305.25O.
2025-06-02 21:03:06,724 [INFO] - --- File 16/48: LD900_25APR17_075305.25O (from .\LD900_25APR17_075305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:06,724 [INFO] - Processing: LD900_25APR17_075305.25O (from .\LD900_25APR17_075305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:06,724 [INFO] -   Unique Output Stem: LD900_25APR17_075305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_075305
2025-06-02 21:03:06,724 [INFO] -   Output POS: LD900_25APR17_075305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_075305.pos, STAT: LD900_25APR17_075305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_075305.stat
2025-06-02 21:03:08,737 [INFO] - Successfully processed LD900_25APR17_075305.25O.
2025-06-02 21:03:08,737 [INFO] - --- File 17/48: LD900_25APR17_082305.25O (from .\LD900_25APR17_082305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:08,738 [INFO] - Processing: LD900_25APR17_082305.25O (from .\LD900_25APR17_082305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:08,738 [INFO] -   Unique Output Stem: LD900_25APR17_082305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_082305
2025-06-02 21:03:08,738 [INFO] -   Output POS: LD900_25APR17_082305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_082305.pos, STAT: LD900_25APR17_082305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_082305.stat
2025-06-02 21:03:10,584 [INFO] - Successfully processed LD900_25APR17_082305.25O.
2025-06-02 21:03:10,584 [INFO] - --- File 18/48: LD900_25APR17_085305.25O (from .\LD900_25APR17_085305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:10,584 [INFO] - Processing: LD900_25APR17_085305.25O (from .\LD900_25APR17_085305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:10,584 [INFO] -   Unique Output Stem: LD900_25APR17_085305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_085305
2025-06-02 21:03:10,584 [INFO] -   Output POS: LD900_25APR17_085305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_085305.pos, STAT: LD900_25APR17_085305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_085305.stat
2025-06-02 21:03:12,436 [INFO] - Successfully processed LD900_25APR17_085305.25O.
2025-06-02 21:03:12,436 [INFO] - --- File 19/48: LD900_25APR17_092305.25O (from .\LD900_25APR17_092305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:12,436 [INFO] - Processing: LD900_25APR17_092305.25O (from .\LD900_25APR17_092305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:12,436 [INFO] -   Unique Output Stem: LD900_25APR17_092305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_092305
2025-06-02 21:03:12,436 [INFO] -   Output POS: LD900_25APR17_092305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_092305.pos, STAT: LD900_25APR17_092305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_092305.stat
2025-06-02 21:03:14,308 [INFO] - Successfully processed LD900_25APR17_092305.25O.
2025-06-02 21:03:14,308 [INFO] - --- File 20/48: LD900_25APR17_095305.25O (from .\LD900_25APR17_095305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:14,308 [INFO] - Processing: LD900_25APR17_095305.25O (from .\LD900_25APR17_095305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:14,308 [INFO] -   Unique Output Stem: LD900_25APR17_095305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_095305
2025-06-02 21:03:14,308 [INFO] -   Output POS: LD900_25APR17_095305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_095305.pos, STAT: LD900_25APR17_095305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_095305.stat
2025-06-02 21:03:16,210 [INFO] - Successfully processed LD900_25APR17_095305.25O.
2025-06-02 21:03:16,210 [INFO] - --- File 21/48: LD900_25APR17_102305.25O (from .\LD900_25APR17_102305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:16,210 [INFO] - Processing: LD900_25APR17_102305.25O (from .\LD900_25APR17_102305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:16,210 [INFO] -   Unique Output Stem: LD900_25APR17_102305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_102305
2025-06-02 21:03:16,210 [INFO] -   Output POS: LD900_25APR17_102305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_102305.pos, STAT: LD900_25APR17_102305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_102305.stat
2025-06-02 21:03:18,149 [INFO] - Successfully processed LD900_25APR17_102305.25O.
2025-06-02 21:03:18,149 [INFO] - --- File 22/48: LD900_25APR17_105305.25O (from .\LD900_25APR17_105305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:18,149 [INFO] - Processing: LD900_25APR17_105305.25O (from .\LD900_25APR17_105305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:18,149 [INFO] -   Unique Output Stem: LD900_25APR17_105305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_105305
2025-06-02 21:03:18,149 [INFO] -   Output POS: LD900_25APR17_105305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_105305.pos, STAT: LD900_25APR17_105305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_105305.stat
2025-06-02 21:03:20,030 [INFO] - Successfully processed LD900_25APR17_105305.25O.
2025-06-02 21:03:20,030 [INFO] - --- File 23/48: LD900_25APR17_112305.25O (from .\LD900_25APR17_112305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:20,031 [INFO] - Processing: LD900_25APR17_112305.25O (from .\LD900_25APR17_112305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:20,031 [INFO] -   Unique Output Stem: LD900_25APR17_112305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_112305
2025-06-02 21:03:20,031 [INFO] -   Output POS: LD900_25APR17_112305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_112305.pos, STAT: LD900_25APR17_112305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_112305.stat
2025-06-02 21:03:22,079 [INFO] - Successfully processed LD900_25APR17_112305.25O.
2025-06-02 21:03:22,079 [INFO] - --- File 24/48: LD900_25APR17_115305.25O (from .\LD900_25APR17_115305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:22,079 [INFO] - Processing: LD900_25APR17_115305.25O (from .\LD900_25APR17_115305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:22,080 [INFO] -   Unique Output Stem: LD900_25APR17_115305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_115305
2025-06-02 21:03:22,080 [INFO] -   Output POS: LD900_25APR17_115305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_115305.pos, STAT: LD900_25APR17_115305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_115305.stat
2025-06-02 21:03:24,096 [INFO] - Successfully processed LD900_25APR17_115305.25O.
2025-06-02 21:03:24,096 [INFO] - --- File 25/48: LD900_25APR17_122305.25O (from .\LD900_25APR17_122305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:24,096 [INFO] - Processing: LD900_25APR17_122305.25O (from .\LD900_25APR17_122305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:24,096 [INFO] -   Unique Output Stem: LD900_25APR17_122305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_122305
2025-06-02 21:03:24,096 [INFO] -   Output POS: LD900_25APR17_122305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_122305.pos, STAT: LD900_25APR17_122305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_122305.stat
2025-06-02 21:03:26,086 [INFO] - Successfully processed LD900_25APR17_122305.25O.
2025-06-02 21:03:26,086 [INFO] - --- File 26/48: LD900_25APR17_125305.25O (from .\LD900_25APR17_125305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:26,086 [INFO] - Processing: LD900_25APR17_125305.25O (from .\LD900_25APR17_125305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:26,086 [INFO] -   Unique Output Stem: LD900_25APR17_125305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_125305
2025-06-02 21:03:26,086 [INFO] -   Output POS: LD900_25APR17_125305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_125305.pos, STAT: LD900_25APR17_125305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_125305.stat
2025-06-02 21:03:28,263 [INFO] - Successfully processed LD900_25APR17_125305.25O.
2025-06-02 21:03:28,263 [INFO] - --- File 27/48: LD900_25APR17_132305.25O (from .\LD900_25APR17_132305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:28,263 [INFO] - Processing: LD900_25APR17_132305.25O (from .\LD900_25APR17_132305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:28,263 [INFO] -   Unique Output Stem: LD900_25APR17_132305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_132305
2025-06-02 21:03:28,264 [INFO] -   Output POS: LD900_25APR17_132305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_132305.pos, STAT: LD900_25APR17_132305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_132305.stat
2025-06-02 21:03:30,521 [INFO] - Successfully processed LD900_25APR17_132305.25O.
2025-06-02 21:03:30,521 [INFO] - --- File 28/48: LD900_25APR17_135305.25O (from .\LD900_25APR17_135305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:30,522 [INFO] - Processing: LD900_25APR17_135305.25O (from .\LD900_25APR17_135305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:30,522 [INFO] -   Unique Output Stem: LD900_25APR17_135305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_135305
2025-06-02 21:03:30,522 [INFO] -   Output POS: LD900_25APR17_135305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_135305.pos, STAT: LD900_25APR17_135305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_135305.stat
2025-06-02 21:03:32,928 [INFO] - Successfully processed LD900_25APR17_135305.25O.
2025-06-02 21:03:32,928 [INFO] - --- File 29/48: LD900_25APR17_142305.25O (from .\LD900_25APR17_142305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:32,928 [INFO] - Processing: LD900_25APR17_142305.25O (from .\LD900_25APR17_142305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:32,928 [INFO] -   Unique Output Stem: LD900_25APR17_142305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_142305
2025-06-02 21:03:32,928 [INFO] -   Output POS: LD900_25APR17_142305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_142305.pos, STAT: LD900_25APR17_142305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_142305.stat
2025-06-02 21:03:35,187 [INFO] - Successfully processed LD900_25APR17_142305.25O.
2025-06-02 21:03:35,187 [INFO] - --- File 30/48: LD900_25APR17_145305.25O (from .\LD900_25APR17_145305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:35,188 [INFO] - Processing: LD900_25APR17_145305.25O (from .\LD900_25APR17_145305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:35,188 [INFO] -   Unique Output Stem: LD900_25APR17_145305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_145305
2025-06-02 21:03:35,188 [INFO] -   Output POS: LD900_25APR17_145305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_145305.pos, STAT: LD900_25APR17_145305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_145305.stat
2025-06-02 21:03:37,441 [INFO] - Successfully processed LD900_25APR17_145305.25O.
2025-06-02 21:03:37,442 [INFO] - --- File 31/48: LD900_25APR17_152305.25O (from .\LD900_25APR17_152305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:37,442 [INFO] - Processing: LD900_25APR17_152305.25O (from .\LD900_25APR17_152305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:37,442 [INFO] -   Unique Output Stem: LD900_25APR17_152305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_152305
2025-06-02 21:03:37,442 [INFO] -   Output POS: LD900_25APR17_152305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_152305.pos, STAT: LD900_25APR17_152305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_152305.stat
2025-06-02 21:03:39,746 [INFO] - Successfully processed LD900_25APR17_152305.25O.
2025-06-02 21:03:39,746 [INFO] - --- File 32/48: LD900_25APR17_155305.25O (from .\LD900_25APR17_155305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:39,746 [INFO] - Processing: LD900_25APR17_155305.25O (from .\LD900_25APR17_155305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:39,746 [INFO] -   Unique Output Stem: LD900_25APR17_155305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_155305
2025-06-02 21:03:39,747 [INFO] -   Output POS: LD900_25APR17_155305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_155305.pos, STAT: LD900_25APR17_155305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_155305.stat
2025-06-02 21:03:41,988 [INFO] - Successfully processed LD900_25APR17_155305.25O.
2025-06-02 21:03:41,988 [INFO] - --- File 33/48: LD900_25APR17_162305.25O (from .\LD900_25APR17_162305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:41,988 [INFO] - Processing: LD900_25APR17_162305.25O (from .\LD900_25APR17_162305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:41,988 [INFO] -   Unique Output Stem: LD900_25APR17_162305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_162305
2025-06-02 21:03:41,988 [INFO] -   Output POS: LD900_25APR17_162305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_162305.pos, STAT: LD900_25APR17_162305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_162305.stat
2025-06-02 21:03:43,960 [INFO] - Successfully processed LD900_25APR17_162305.25O.
2025-06-02 21:03:43,960 [INFO] - --- File 34/48: LD900_25APR17_165305.25O (from .\LD900_25APR17_165305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:43,961 [INFO] - Processing: LD900_25APR17_165305.25O (from .\LD900_25APR17_165305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:43,961 [INFO] -   Unique Output Stem: LD900_25APR17_165305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_165305
2025-06-02 21:03:43,961 [INFO] -   Output POS: LD900_25APR17_165305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_165305.pos, STAT: LD900_25APR17_165305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_165305.stat
2025-06-02 21:03:45,893 [INFO] - Successfully processed LD900_25APR17_165305.25O.
2025-06-02 21:03:45,893 [INFO] - --- File 35/48: LD900_25APR17_172305.25O (from .\LD900_25APR17_172305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:45,893 [INFO] - Processing: LD900_25APR17_172305.25O (from .\LD900_25APR17_172305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:45,893 [INFO] -   Unique Output Stem: LD900_25APR17_172305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_172305
2025-06-02 21:03:45,894 [INFO] -   Output POS: LD900_25APR17_172305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_172305.pos, STAT: LD900_25APR17_172305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_172305.stat
2025-06-02 21:03:47,801 [INFO] - Successfully processed LD900_25APR17_172305.25O.
2025-06-02 21:03:47,801 [INFO] - --- File 36/48: LD900_25APR17_175305.25O (from .\LD900_25APR17_175305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:47,801 [INFO] - Processing: LD900_25APR17_175305.25O (from .\LD900_25APR17_175305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:47,801 [INFO] -   Unique Output Stem: LD900_25APR17_175305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_175305
2025-06-02 21:03:47,801 [INFO] -   Output POS: LD900_25APR17_175305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_175305.pos, STAT: LD900_25APR17_175305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_175305.stat
2025-06-02 21:03:49,738 [INFO] - Successfully processed LD900_25APR17_175305.25O.
2025-06-02 21:03:49,738 [INFO] - --- File 37/48: LD900_25APR17_182305.25O (from .\LD900_25APR17_182305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:49,738 [INFO] - Processing: LD900_25APR17_182305.25O (from .\LD900_25APR17_182305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:49,738 [INFO] -   Unique Output Stem: LD900_25APR17_182305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_182305
2025-06-02 21:03:49,738 [INFO] -   Output POS: LD900_25APR17_182305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_182305.pos, STAT: LD900_25APR17_182305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_182305.stat
2025-06-02 21:03:51,721 [INFO] - Successfully processed LD900_25APR17_182305.25O.
2025-06-02 21:03:51,721 [INFO] - --- File 38/48: LD900_25APR17_185305.25O (from .\LD900_25APR17_185305\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:51,722 [INFO] - Processing: LD900_25APR17_185305.25O (from .\LD900_25APR17_185305\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:51,722 [INFO] -   Unique Output Stem: LD900_25APR17_185305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_185305
2025-06-02 21:03:51,722 [INFO] -   Output POS: LD900_25APR17_185305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_185305.pos, STAT: LD900_25APR17_185305_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_185305.stat
2025-06-02 21:03:53,699 [INFO] - Successfully processed LD900_25APR17_185305.25O.
2025-06-02 21:03:53,700 [INFO] - --- File 39/48: LD900_25APR17_192306.25O (from .\LD900_25APR17_192306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:53,700 [INFO] - Processing: LD900_25APR17_192306.25O (from .\LD900_25APR17_192306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:53,700 [INFO] -   Unique Output Stem: LD900_25APR17_192306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_192306
2025-06-02 21:03:53,700 [INFO] -   Output POS: LD900_25APR17_192306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_192306.pos, STAT: LD900_25APR17_192306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_192306.stat
2025-06-02 21:03:55,868 [INFO] - Successfully processed LD900_25APR17_192306.25O.
2025-06-02 21:03:55,868 [INFO] - --- File 40/48: LD900_25APR17_195306.25O (from .\LD900_25APR17_195306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:55,868 [INFO] - Processing: LD900_25APR17_195306.25O (from .\LD900_25APR17_195306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:55,868 [INFO] -   Unique Output Stem: LD900_25APR17_195306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_195306
2025-06-02 21:03:55,868 [INFO] -   Output POS: LD900_25APR17_195306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_195306.pos, STAT: LD900_25APR17_195306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_195306.stat
2025-06-02 21:03:58,085 [INFO] - Successfully processed LD900_25APR17_195306.25O.
2025-06-02 21:03:58,086 [INFO] - --- File 41/48: LD900_25APR17_202306.25O (from .\LD900_25APR17_202306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:03:58,086 [INFO] - Processing: LD900_25APR17_202306.25O (from .\LD900_25APR17_202306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:03:58,086 [INFO] -   Unique Output Stem: LD900_25APR17_202306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_202306
2025-06-02 21:03:58,086 [INFO] -   Output POS: LD900_25APR17_202306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_202306.pos, STAT: LD900_25APR17_202306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_202306.stat
2025-06-02 21:04:00,415 [INFO] - Successfully processed LD900_25APR17_202306.25O.
2025-06-02 21:04:00,415 [INFO] - --- File 42/48: LD900_25APR17_205306.25O (from .\LD900_25APR17_205306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:00,415 [INFO] - Processing: LD900_25APR17_205306.25O (from .\LD900_25APR17_205306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:00,415 [INFO] -   Unique Output Stem: LD900_25APR17_205306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_205306
2025-06-02 21:04:00,416 [INFO] -   Output POS: LD900_25APR17_205306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_205306.pos, STAT: LD900_25APR17_205306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_205306.stat
2025-06-02 21:04:02,791 [INFO] - Successfully processed LD900_25APR17_205306.25O.
2025-06-02 21:04:02,791 [INFO] - --- File 43/48: LD900_25APR17_212306.25O (from .\LD900_25APR17_212306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:02,792 [INFO] - Processing: LD900_25APR17_212306.25O (from .\LD900_25APR17_212306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:02,792 [INFO] -   Unique Output Stem: LD900_25APR17_212306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_212306
2025-06-02 21:04:02,792 [INFO] -   Output POS: LD900_25APR17_212306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_212306.pos, STAT: LD900_25APR17_212306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_212306.stat
2025-06-02 21:04:05,233 [INFO] - Successfully processed LD900_25APR17_212306.25O.
2025-06-02 21:04:05,234 [INFO] - --- File 44/48: LD900_25APR17_215306.25O (from .\LD900_25APR17_215306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:05,234 [INFO] - Processing: LD900_25APR17_215306.25O (from .\LD900_25APR17_215306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:05,234 [INFO] -   Unique Output Stem: LD900_25APR17_215306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_215306
2025-06-02 21:04:05,234 [INFO] -   Output POS: LD900_25APR17_215306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_215306.pos, STAT: LD900_25APR17_215306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_215306.stat
2025-06-02 21:04:07,708 [INFO] - Successfully processed LD900_25APR17_215306.25O.
2025-06-02 21:04:07,708 [INFO] - --- File 45/48: LD900_25APR17_222306.25O (from .\LD900_25APR17_222306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:07,708 [INFO] - Processing: LD900_25APR17_222306.25O (from .\LD900_25APR17_222306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:07,708 [INFO] -   Unique Output Stem: LD900_25APR17_222306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_222306
2025-06-02 21:04:07,708 [INFO] -   Output POS: LD900_25APR17_222306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_222306.pos, STAT: LD900_25APR17_222306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_222306.stat
2025-06-02 21:04:10,146 [INFO] - Successfully processed LD900_25APR17_222306.25O.
2025-06-02 21:04:10,146 [INFO] - --- File 46/48: LD900_25APR17_225306.25O (from .\LD900_25APR17_225306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:10,147 [INFO] - Processing: LD900_25APR17_225306.25O (from .\LD900_25APR17_225306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:10,147 [INFO] -   Unique Output Stem: LD900_25APR17_225306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_225306
2025-06-02 21:04:10,147 [INFO] -   Output POS: LD900_25APR17_225306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_225306.pos, STAT: LD900_25APR17_225306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_225306.stat
2025-06-02 21:04:12,505 [INFO] - Successfully processed LD900_25APR17_225306.25O.
2025-06-02 21:04:12,505 [INFO] - --- File 47/48: LD900_25APR17_232306.25O (from .\LD900_25APR17_232306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:12,505 [INFO] - Processing: LD900_25APR17_232306.25O (from .\LD900_25APR17_232306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:12,505 [INFO] -   Unique Output Stem: LD900_25APR17_232306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_232306
2025-06-02 21:04:12,505 [INFO] -   Output POS: LD900_25APR17_232306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_232306.pos, STAT: LD900_25APR17_232306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_232306.stat
2025-06-02 21:04:14,708 [INFO] - Successfully processed LD900_25APR17_232306.25O.
2025-06-02 21:04:14,708 [INFO] - --- File 48/48: LD900_25APR17_235306.25O (from .\LD900_25APR17_235306\Converted_On_20250602_T1559\RINEXv3_04) ---
2025-06-02 21:04:14,709 [INFO] - Processing: LD900_25APR17_235306.25O (from .\LD900_25APR17_235306\Converted_On_20250602_T1559\RINEXv3_04)
2025-06-02 21:04:14,709 [INFO] -   Unique Output Stem: LD900_25APR17_235306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_235306
2025-06-02 21:04:14,709 [INFO] -   Output POS: LD900_25APR17_235306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_235306.pos, STAT: LD900_25APR17_235306_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_235306.stat
2025-06-02 21:04:16,854 [INFO] - Successfully processed LD900_25APR17_235306.25O.
2025-06-02 21:04:16,854 [INFO] - 
--- RINEX File Processing Summary ---
2025-06-02 21:04:16,855 [INFO] - Successfully processed: 48 files.
2025-06-02 21:04:16,855 [INFO] - Failed to process: 0 files.
2025-06-02 21:04:16,855 [INFO] - Combining 48 .pos files into combined_dataset_spp.pos...
2025-06-02 21:04:16,859 [INFO] - Using header from (header-only file): LD900_25APR17_002304_Converted_On_20250602_T1559_RINEXv3_04_LD900_25APR17_002304.pos
2025-06-02 21:04:16,863 [INFO] - Successfully combined .pos files: combined_dataset_spp.pos
2025-06-02 21:04:16,863 [INFO] - Generating KML from combined_dataset_spp.pos to combined_dataset_spp.kml...
2025-06-02 21:04:16,894 [ERROR] - Error generating KML. pos2kml code 0.
2025-06-02 21:04:16,894 [ERROR] -   STDOUT:  STDERR: usage: pos2kml [option]... file [...]

 Read solution file(s) and convert it to Google Earth KML file or GPX file.
 Each line in the input file shall contain fields of time, position fields 
 (latitude/longitude/height or x/y/z-ecef), and quality flag(option). The line
 started with '%', '#', ';' is treated as comment. Command options are as 
 follows. ([]:default)

 -h        print help
 -o file   output file [infile + .kml]
 -c color  track color (0:off,1:white,2:green,3:orange,4:red,5:yellow) [5]
 -p color  point color (0:off,1:white,2:green,3:orange,4:red,5:by qflag) [5]
 -a        output altitude information [off]
 -ag       output geodetic altitude [off]
 -tg       output time stamp of gpst [off]
 -tu       output time stamp of utc [gpst]
 -i tint   output time interval (s) (0:all) [0]
 -q qflg   output q-flags (0:all) [0]
 -f n e h  add north/east/height offset to position (m) [0 0 0]
 -gpx      output GPX file
2025-06-02 21:04:16,894 [INFO] - 
--- Script Finished ---
2025-06-02 21:04:16,894 [INFO] - Final combined POS: C:\Dev\RTKLIB\Outputs\combined_dataset_spp.pos
2025-06-02 21:04:16,895 [INFO] - Final KML: Not created/empty
2025-06-02 21:04:16,895 [INFO] - Log file: C:\Dev\RTKLIB\Outputs\processing_log_spp.txt
2025-06-02 21:04:16,895 [INFO] - Individual POS/STAT files are in: C:\Dev\RTKLIB\Outputs\individual_pos_files_spp
